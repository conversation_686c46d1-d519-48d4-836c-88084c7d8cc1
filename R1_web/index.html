<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B站UP主数据看板</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary: #00A1D6;
            --primary-dark: #0087b3;
            --secondary: #FB7299;
            --dark: #1A1A1A;
            --darker: #121212;
            --light: #F5F5F5;
            --gray: #8A8A8A;
            --success: #52C41A;
            --warning: #FAAD14;
            --danger: #F5222D;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--darker);
            color: var(--light);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* 头部样式 */
        header {
            background-color: var(--dark);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo img {
            height: 40px;
            border-radius: 50%;
        }
        
        .logo h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background-color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        /* 控制面板样式 */
        .control-panel {
            background-color: var(--dark);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .date-selector, .data-filter {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        select, button {
            padding: 10px 15px;
            background-color: var(--darker);
            color: var(--light);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            font-family: 'Noto Sans SC', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        select:focus, button:focus {
            outline: none;
            border-color: var(--primary);
        }
        
        button {
            background-color: var(--primary);
            font-weight: 500;
        }
        
        button:hover {
            background-color: var(--primary-dark);
        }
        
        /* KPI卡片样式 */
        .kpi-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .kpi-card {
            background-color: var(--dark);
            border-radius: 10px;
            padding: 25px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            transition: transform 0.3s ease;
        }
        
        .kpi-card:hover {
            transform: translateY(-5px);
        }
        
        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .kpi-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
        
        .kpi-value {
            font-size: 2.2rem;
            font-weight: 700;
        }
        
        .kpi-title {
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        .kpi-trend {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
        }
        
        .trend-up {
            color: var(--success);
        }
        
        .trend-down {
            color: var(--danger);
        }
        
        /* 图表容器样式 */
        .chart-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .chart-card {
            background-color: var(--dark);
            border-radius: 10px;
            padding: 25px;
        }
        
        .chart-header {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chart-title {
            font-size: 1.2rem;
            font-weight: 500;
        }
        
        .chart-actions {
            display: flex;
            gap: 10px;
        }
        
        .chart-actions button {
            padding: 5px 10px;
            font-size: 0.8rem;
        }
        
        .chart-wrapper {
            height: 300px;
            position: relative;
        }
        
        /* 页脚样式 */
        footer {
            background-color: var(--dark);
            padding: 30px 0;
            margin-top: 40px;
            text-align: center;
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .chart-container {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .control-panel {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- 头部区域 -->
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fab fa-bilibili" style="font-size: 2.5rem; color: #00A1D6;"></i>
                    <h1>UP主数据看板</h1>
                </div>
                <div class="user-info">
                    <div class="user-avatar">UP</div>
                    <div>
                        <div style="font-weight: 500;">UP主昵称</div>
                        <div style="font-size: 0.8rem; color: var(--gray)">粉丝数: 125,678</div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    
    <main class="container">
        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="date-selector">
                <label for="date-range">时间范围:</label>
                <select id="date-range">
                    <option>最近7天</option>
                    <option selected>最近30天</option>
                    <option>最近90天</option>
                    <option>自定义范围</option>
                </select>
            </div>
            <div class="data-filter">
                <label for="content-type">内容类型:</label>
                <select id="content-type">
                    <option>全部内容</option>
                    <option>视频</option>
                    <option>专栏</option>
                    <option>动态</option>
                </select>
            </div>
            <button id="refresh-btn">
                <i class="fas fa-sync-alt"></i> 刷新数据
            </button>
            <button id="export-btn">
                <i class="fas fa-download"></i> 导出报告
            </button>
        </div>
        
        <!-- KPI指标卡片 -->
        <div class="kpi-container">
            <div class="kpi-card">
                <div class="kpi-header">
                    <div class="kpi-icon" style="background-color: rgba(0, 161, 214, 0.2);">
                        <i class="fas fa-play-circle" style="color: var(--primary);"></i>
                    </div>
                    <div class="kpi-trend trend-up">
                        <i class="fas fa-arrow-up"></i> 12.5%
                    </div>
                </div>
                <div class="kpi-value">1,245,678</div>
                <div class="kpi-title">总播放量</div>
            </div>
            
            <div class="kpi-card">
                <div class="kpi-header">
                    <div class="kpi-icon" style="background-color: rgba(251, 114, 153, 0.2);">
                        <i class="fas fa-users" style="color: var(--secondary);"></i>
                    </div>
                    <div class="kpi-trend trend-up">
                        <i class="fas fa-arrow-up"></i> 8.3%
                    </div>
                </div>
                <div class="kpi-value">5,432</div>
                <div class="kpi-title">新增粉丝</div>
            </div>
            
            <div class="kpi-card">
                <div class="kpi-header">
                    <div class="kpi-icon" style="background-color: rgba(82, 196, 26, 0.2);">
                        <i class="fas fa-heart" style="color: var(--success);"></i>
                    </div>
                    <div class="kpi-trend trend-up">
                        <i class="fas fa-arrow-up"></i> 5.7%
                    </div>
                </div>
                <div class="kpi-value">98,765</div>
                <div class="kpi-title">点赞数</div>
            </div>
            
            <div class="kpi-card">
                <div class="kpi-header">
                    <div class="kpi-icon" style="background-color: rgba(250, 173, 20, 0.2);">
                        <i class="fas fa-comment-alt" style="color: var(--warning);"></i>
                    </div>
                    <div class="kpi-trend trend-down">
                        <i class="fas fa-arrow-down"></i> 3.2%
                    </div>
                </div>
                <div class="kpi-value">12,345</div>
                <div class="kpi-title">评论数</div>
            </div>
        </div>
        
        <!-- 图表区域 -->
        <div class="chart-container">
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">播放量趋势</div>
                    <div class="chart-actions">
                        <button>7天</button>
                        <button class="active">30天</button>
                        <button>90天</button>
                    </div>
                </div>
                <div class="chart-wrapper">
                    <canvas id="play-trend-chart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">粉丝增长分析</div>
                    <div class="chart-actions">
                        <button>日</button>
                        <button class="active">周</button>
                        <button>月</button>
                    </div>
                </div>
                <div class="chart-wrapper">
                    <canvas id="fans-growth-chart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">互动数据分布</div>
                </div>
                <div class="chart-wrapper">
                    <canvas id="interaction-chart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">视频类型表现</div>
                </div>
                <div class="chart-wrapper">
                    <canvas id="content-type-chart"></canvas>
                </div>
            </div>
        </div>
    </main>
    
    <!-- 页脚 -->
    <footer>
        <div class="container">
            <p>© 2023 B站UP主数据看板 | 数据更新时间: 2023-10-15 14:30</p>
            <p>本数据仅供参考，实际数据以B站官方后台为准</p>
        </div>
    </footer>
    
    <script>
        // 模拟数据
        const dates = [];
        for (let i = 29; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            dates.push(`${date.getMonth() + 1}/${date.getDate()}`);
        }
        
        // 播放量趋势图
        const playTrendCtx = document.getElementById('play-trend-chart').getContext('2d');
        const playTrendChart = new Chart(playTrendCtx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [{
                    label: '播放量',
                    data: [12500, 13200, 11800, 14500, 13800, 15200, 14800, 16200, 15500, 14300, 15600, 14800, 16200, 17500, 16800, 18200, 17500, 18800, 19200, 18500, 19800, 20500, 19200, 18500, 17800, 19200, 20500, 21800, 22500, 23800],
                    borderColor: '#00A1D6',
                    backgroundColor: 'rgba(0, 161, 214, 0.1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#8A8A8A'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#8A8A8A',
                            maxTicksLimit: 10
                        }
                    }
                }
            }
        });
        
        // 粉丝增长图
        const fansGrowthCtx = document.getElementById('fans-growth-chart').getContext('2d');
        const fansGrowthChart = new Chart(fansGrowthCtx, {
            type: 'bar',
            data: {
                labels: ['第1周', '第2周', '第3周', '第4周'],
                datasets: [{
                    label: '新增粉丝',
                    data: [1250, 1320, 1480, 1630],
                    backgroundColor: '#FB7299',
                    borderRadius: 5
                }, {
                    label: '取关粉丝',
                    data: [120, 95, 110, 105],
                    backgroundColor: '#8A8A8A',
                    borderRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#8A8A8A'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#8A8A8A'
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: '#F5F5F5'
                        }
                    }
                }
            }
        });
        
        // 互动数据图
        const interactionCtx = document.getElementById('interaction-chart').getContext('2d');
        const interactionChart = new Chart(interactionCtx, {
            type: 'doughnut',
            data: {
                labels: ['点赞', '投币', '收藏', '分享', '评论'],
                datasets: [{
                    data: [45, 20, 15, 10, 10],
                    backgroundColor: [
                        '#52C41A',
                        '#00A1D6',
                        '#FAAD14',
                        '#FB7299',
                        '#8A8A8A'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            color: '#F5F5F5',
                            padding: 15
                        }
                    }
                }
            }
        });
        
        // 视频类型图
        const contentTypeCtx = document.getElementById('content-type-chart').getContext('2d');
        const contentTypeChart = new Chart(contentTypeCtx, {
            type: 'radar',
            data: {
                labels: ['游戏', '科技', '生活', '知识', '动画', '音乐', '影视'],
                datasets: [{
                    label: '播放量',
                    data: [85, 70, 65, 60, 75, 50, 55],
                    fill: true,
                    backgroundColor: 'rgba(0, 161, 214, 0.2)',
                    borderColor: '#00A1D6',
                    pointBackgroundColor: '#00A1D6',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#00A1D6'
                }, {
                    label: '互动率',
                    data: [75, 80, 70, 85, 65, 75, 60],
                    fill: true,
                    backgroundColor: 'rgba(251, 114, 153, 0.2)',
                    borderColor: '#FB7299',
                    pointBackgroundColor: '#FB7299',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#FB7299'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        angleLines: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        pointLabels: {
                            color: '#8A8A8A'
                        },
                        ticks: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: '#F5F5F5'
                        }
                    }
                }
            }
        });
        
        // 按钮交互
        document.querySelectorAll('.chart-actions button').forEach(button => {
            button.addEventListener('click', function() {
                this.parentElement.querySelectorAll('button').forEach(btn => {
                    btn.classList.remove('active');
                });
                this.classList.add('active');
            });
        });
        
        document.getElementById('refresh-btn').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 更新中...';
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-sync-alt"></i> 刷新数据';
                alert('数据已更新！');
            }, 1500);
        });
    </script>
</body>
</html>