<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>墨心艺术馆 | 藏品画廊</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Lato:wght@300;400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #1a365d; /* 美术馆深蓝色 */
            --text-dark: #333;
            --text-light: #777;
            --bg-light: #f8f9fa;
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Lato', sans-serif;
            color: var(--text-dark);
            line-height: 1.6;
            background-color: white;
        }
        
        h1, h2, h3 {
            font-family: 'Playfair Display', serif;
            font-weight: 700;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        
        /* 头部导航 */
        header {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
        }
        
        .logo {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem;
            color: var(--primary-color);
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
        }
        
        .nav-menu li {
            margin-left: 30px;
        }
        
        .nav-menu a {
            position: relative;
            padding-bottom: 5px;
            font-weight: 700;
        }
        
        .nav-menu a:hover::after,
        .nav-menu a.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }
        
        .hamburger {
            display: none;
            cursor: pointer;
            font-size: 1.5rem;
        }
        
        /* 画廊网格 */
        .gallery {
            padding: 50px 0;
        }
        
        .gallery-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .gallery-header h1 {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 30px;
        }
        
        .artwork {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: var(--transition);
        }
        
        .artwork:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .artwork-img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: var(--transition);
        }
        
        .artwork:hover .artwork-img {
            transform: scale(1.05);
        }
        
        .artwork-info {
            padding: 20px;
        }
        
        .artwork-title {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
        
        .artwork-artist {
            color: var(--text-light);
            font-style: italic;
        }
        
        /* 页脚 */
        footer {
            background-color: var(--bg-light);
            padding: 60px 0 30px;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .footer-section h3 {
            font-size: 1.4rem;
            margin-bottom: 20px;
            color: var(--primary-color);
        }
        
        .social-links {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .social-links a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            transition: var(--transition);
        }
        
        .social-links a:hover {
            transform: translateY(-3px);
        }
        
        .copyright {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid #ddd;
            color: var(--text-light);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background-color: white;
                flex-direction: column;
                box-shadow: 0 5px 10px rgba(0,0,0,0.1);
            }
            
            .nav-menu.active {
                display: flex;
            }
            
            .nav-menu li {
                margin: 0;
                padding: 15px 20px;
                border-bottom: 1px solid #eee;
            }
            
            .hamburger {
                display: block;
            }
            
            .gallery-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header>
        <div class="container header-container">
            <div class="logo">墨心艺术馆</div>
            <ul class="nav-menu">
                <li><a href="#" class="active">首页</a></li>
                <li><a href="#">展览</a></li>
                <li><a href="#">藏品</a></li>
                <li><a href="#">关于</a></li>
                <li><a href="#">联系</a></li>
            </ul>
            <div class="hamburger">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- 画廊主体 -->
    <main class="gallery">
        <div class="container">
            <div class="gallery-header">
                <h1>精选藏品</h1>
                <p>探索我们珍贵的艺术收藏</p>
            </div>
            
            <div class="gallery-grid">
                <!-- 艺术品 1 -->
                <div class="artwork">
                    <img src="https://picsum.photos/600/400?random=1" alt="艺术品" class="artwork-img">
                    <div class="artwork-info">
                        <h3 class="artwork-title">晨曦</h3>
                        <p class="artwork-artist">张明 | 2020</p>
                    </div>
                </div>
                
                <!-- 艺术品 2 -->
                <div class="artwork">
                    <img src="https://picsum.photos/600/400?random=2" alt="艺术品" class="artwork-img">
                    <div class="artwork-info">
                        <h3 class="artwork-title">城市韵律</h3>
                        <p class="artwork-artist">李华 | 2018</p>
                    </div>
                </div>
                
                <!-- 艺术品 3 -->
                <div class="artwork">
                    <img src="https://picsum.photos/600/400?random=3" alt="艺术品" class="artwork-img">
                    <div class="artwork-info">
                        <h3 class="artwork-title">山水之间</h3>
                        <p class="artwork-artist">王维 | 2019</p>
                    </div>
                </div>
                
                <!-- 艺术品 4 -->
                <div class="artwork">
                    <img src="https://picsum.photos/600/400?random=4" alt="艺术品" class="artwork-img">
                    <div class="artwork-info">
                        <h3 class="artwork-title">静物</h3>
                        <p class="artwork-artist">陈静 | 2021</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>关于我们</h3>
                    <p>墨心艺术馆成立于2010年，致力于展示和推广中国当代艺术，为艺术家和艺术爱好者搭建交流平台。</p>
                </div>
                
                <div class="footer-section">
                    <h3>联系方式</h3>
                    <p><i class="fas fa-map-marker-alt"></i> 北京市朝阳区艺术区88号</p>
                    <p><i class="fas fa-phone"></i> (010) 8888-7777</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    
                    <div class="social-links">
                        <a href="#"><i class="fab fa-weixin"></i></a>
                        <a href="#"><i class="fab fa-weibo"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h3>开放时间</h3>
                    <p>周二至周日: 10:00 - 18:00</p>
                    <p>周一闭馆</p>
                    <p>节假日特殊安排请关注公告</p>
                </div>
            </div>
            
            <div class="copyright">
                <p>&copy; 2023 墨心艺术馆 版权所有</p>
            </div>
        </div>
    </footer>

    <script>
        // 汉堡菜单交互
        document.querySelector('.hamburger').addEventListener('click', function() {
            document.querySelector('.nav-menu').classList.toggle('active');
        });
        
        // 图片灯箱效果（简化版）
        document.querySelectorAll('.artwork-img').forEach(img => {
            img.addEventListener('click', function() {
                alert('艺术品详情功能将在后续版本实现');
            });
        });
    </script>
</body>
</html>