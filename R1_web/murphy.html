<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Xu - IT 专业人士</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@600;700&family=Inter:wght@400;500&family=Fira+Code&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary: #2563eb;
            --accent: #f59e0b;
            --text: #1e293b;
            --bg: #f8fafc;
            --card: #ffffff;
            --shadow: rgba(0, 0, 0, 0.05);
            --transition: all 0.3s ease;
        }

        .dark-mode {
            --text: #e2e8f0;
            --bg: #0f172a;
            --card: #1e293b;
            --shadow: rgba(0, 0, 0, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            transition: var(--transition);
        }

        body {
            font-family: 'Inter', sans-serif;
            color: var(--text);
            background: var(--bg);
            line-height: 1.6;
            scroll-behavior: smooth;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        /* 头部导航 */
        header {
            position: fixed;
            top: 0;
            width: 100%;
            background: var(--card);
            box-shadow: 0 2px 10px var(--shadow);
            z-index: 100;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 0;
        }

        .logo {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text);
            font-weight: 500;
            position: relative;
        }

        .nav-links a:after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--accent);
            transition: var(--transition);
        }

        .nav-links a:hover:after {
            width: 100%;
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--text);
            cursor: pointer;
            font-size: 1.2rem;
        }

        /* 英雄区域 */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding-top: 6rem;
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
        }

        .hero-text h1 {
            font-family: 'Montserrat', sans-serif;
            font-size: 3.5rem;
            line-height: 1.2;
            margin-bottom: 1.5rem;
        }

        .highlight {
            color: var(--primary);
            position: relative;
            display: inline-block;
        }

        .highlight:after {
            content: '';
            position: absolute;
            bottom: 5px;
            left: 0;
            width: 100%;
            height: 10px;
            background: rgba(37, 99, 235, 0.2);
            z-index: -1;
        }

        .hero-text p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            max-width: 90%;
        }

        .btn {
            display: inline-block;
            padding: 0.8rem 2rem;
            background: var(--primary);
            color: white;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 4px 6px var(--shadow);
            transition: var(--transition);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px var(--shadow);
        }

        .hero-image {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px var(--shadow);
            height: 400px;
            background: linear-gradient(135deg, var(--primary), #7c3aed);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hero-image i {
            font-size: 10rem;
            color: rgba(255, 255, 255, 0.9);
            animation: pulse 3s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* 内容区块 */
        section {
            padding: 6rem 0;
        }

        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            position: relative;
            display: inline-block;
        }

        .section-title:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 60px;
            height: 4px;
            background: var(--accent);
            border-radius: 2px;
        }

        /* 关于我 */
        .about-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
        }

        .about-text p {
            margin-bottom: 1.5rem;
        }

        .skills {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
        }

        .skill-item h4 {
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .skill-bar {
            height: 10px;
            background: #e2e8f0;
            border-radius: 5px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: var(--primary);
            border-radius: 5px;
            position: relative;
        }

        .skill-progress:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shine 2s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* 工作经历 */
        .timeline {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }

        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary);
            left: 50%;
            transform: translateX(-50%);
        }

        .timeline-item {
            display: flex;
            margin-bottom: 3rem;
            position: relative;
        }

        .timeline-item:nth-child(odd) {
            justify-content: flex-start;
            padding-right: calc(50% + 2rem);
            text-align: right;
        }

        .timeline-item:nth-child(even) {
            justify-content: flex-end;
            padding-left: calc(50% + 2rem);
        }

        .timeline-content {
            background: var(--card);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px var(--shadow);
            width: 100%;
            max-width: 400px;
            position: relative;
        }

        .timeline-content:before {
            content: '';
            position: absolute;
            top: 24px;
            width: 20px;
            height: 20px;
            background: var(--card);
            transform: rotate(45deg);
        }

        .timeline-item:nth-child(odd) .timeline-content:before {
            right: -10px;
        }

        .timeline-item:nth-child(even) .timeline-content:before {
            left: -10px;
        }

        .timeline-date {
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        /* 项目展示 */
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
        }

        .project-card {
            background: var(--card);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px var(--shadow);
            transition: var(--transition);
        }

        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 15px var(--shadow);
        }

        .project-image {
            height: 200px;
            background: linear-gradient(135deg, var(--primary), #7c3aed);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }

        .project-info {
            padding: 1.5rem;
        }

        .project-info h3 {
            margin-bottom: 0.5rem;
        }

        .project-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .tag {
            background: rgba(37, 99, 235, 0.1);
            color: var(--primary);
            padding: 0.3rem 0.8rem;
            border-radius: 50px;
            font-size: 0.85rem;
        }

        /* 联系表单 */
        .contact-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            background: rgba(37, 99, 235, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-size: 1.2rem;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-link {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--card);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            text-decoration: none;
            box-shadow: 0 2px 4px var(--shadow);
            transition: var(--transition);
        }

        .social-link:hover {
            transform: translateY(-3px);
            background: var(--primary);
            color: white;
        }

        .contact-form .form-group {
            margin-bottom: 1.5rem;
        }

        .contact-form input,
        .contact-form textarea {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: var(--card);
            color: var(--text);
        }

        .contact-form textarea {
            min-height: 150px;
            resize: vertical;
        }

        /* 页脚 */
        footer {
            background: var(--card);
            padding: 3rem 0;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 992px) {
            .hero-content,
            .about-content,
            .contact-container {
                grid-template-columns: 1fr;
            }
            
            .hero-text {
                order: 2;
            }
            
            .hero-image {
                order: 1;
                margin-bottom: 2rem;
            }
            
            .timeline:before {
                left: 30px;
            }
            
            .timeline-item:nth-child(odd),
            .timeline-item:nth-child(even) {
                padding: 0 0 0 70px;
                text-align: left;
                justify-content: flex-start;
            }
            
            .timeline-item:nth-child(odd) .timeline-content:before,
            .timeline-item:nth-child(even) .timeline-content:before {
                left: -10px;
                right: auto;
            }
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .skills {
                grid-template-columns: 1fr;
            }
            
            .mobile-menu-btn {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header>
        <div class="container">
            <nav>
                <div class="logo">Murphy.X</div>
                <div class="nav-links">
                    <a href="#about">关于我</a>
                    <a href="#experience">工作经历</a>
                    <a href="#projects">项目</a>
                    <a href="#contact">联系</a>
                </div>
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
            </nav>
        </div>
    </header>

    <!-- 英雄区域 -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>你好，我是 <span class="highlight">Murphy Xu</span></h1>
                    <p>IT解决方案架构师 & 全栈开发者，专注于构建高效、可扩展的技术系统，用代码解决实际问题。</p>
                    <a href="#contact" class="btn">联系我</a>
                </div>
                <div class="hero-image">
                    <i class="fas fa-laptop-code"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- 关于我 -->
    <section id="about">
        <div class="container">
            <h2 class="section-title">关于我</h2>
            <div class="about-content">
                <div class="about-text">
                    <p>拥有10年IT行业经验，专注于云计算架构和全栈开发。热衷于技术创新，善于将复杂问题转化为简洁高效的解决方案。</p>
                    <p>我相信技术应该服务于人，因此在开发过程中始终关注用户体验和实际价值。工作之余，我积极参与开源社区，并指导新人进入IT行业。</p>
                </div>
                <div class="skills">
                    <div class="skill-item">
                        <h4>云架构设计</h4>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 95%"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <h4>前端开发</h4>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 90%"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <h4>后端开发</h4>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 92%"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <h4>DevOps</h4>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 88%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 工作经历 -->
    <section id="experience" style="background: rgba(37, 99, 235, 0.03);">
        <div class="container">
            <h2 class="section-title">工作经历</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-date">2020 - 至今</div>
                        <h3>高级解决方案架构师</h3>
                        <p>科技先锋有限公司</p>
                        <p>负责企业级云架构设计，优化系统性能，降低运维成本30%。领导15人技术团队实施敏捷开发。</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-date">2017 - 2020</div>
                        <h3>全栈开发工程师</h3>
                        <p>创新科技有限公司</p>
                        <p>设计并开发了公司核心SaaS产品，用户增长300%。实现微服务架构转型，提高系统稳定性。</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-date">2014 - 2017</div>
                        <h3>后端开发工程师</h3>
                        <p>数字未来有限公司</p>
                        <p>构建高并发API服务，支持日活用户超50万。优化数据库查询，使响应时间减少60%。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 项目展示 -->
    <section id="projects">
        <div class="container">
            <h2 class="section-title">精选项目</h2>
            <div class="projects-grid">
                <div class="project-card">
                    <div class="project-image">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <div class="project-info">
                        <h3>云迁移平台</h3>
                        <p>为企业提供一站式云迁移解决方案，支持多云环境无缝迁移。</p>
                        <div class="project-tags">
                            <span class="tag">AWS</span>
                            <span class="tag">Docker</span>
                            <span class="tag">Kubernetes</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="project-info">
                        <h3>智能数据分析平台</h3>
                        <p>实时数据处理和可视化系统，帮助企业做出数据驱动的决策。</p>
                        <div class="project-tags">
                            <span class="tag">Python</span>
                            <span class="tag">React</span>
                            <span class="tag">Elasticsearch</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="project-info">
                        <h3>移动办公解决方案</h3>
                        <p>跨平台移动应用，集成办公自动化工具，提高团队协作效率。</p>
                        <div class="project-tags">
                            <span class="tag">Flutter</span>
                            <span class="tag">Firebase</span>
                            <span class="tag">Node.js</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系表单 -->
    <section id="contact" style="background: rgba(245, 158, 11, 0.03);">
        <div class="container">
            <h2 class="section-title">联系我</h2>
            <div class="contact-container">
                <div class="contact-info">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div>
                            <h3>邮箱</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <h3>地点</h3>
                            <p>中国，上海</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-share-alt"></i>
                        </div>
                        <div>
                            <h3>社交媒体</h3>
                            <div class="social-links">
                                <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="contact-form">
                    <form id="contactForm">
                        <div class="form-group">
                            <input type="text" placeholder="姓名" required>
                        </div>
                        <div class="form-group">
                            <input type="email" placeholder="邮箱" required>
                        </div>
                        <div class="form-group">
                            <input type="text" placeholder="主题">
                        </div>
                        <div class="form-group">
                            <textarea placeholder="留言"></textarea>
                        </div>
                        <button type="submit" class="btn">发送消息</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <p>&copy; 2023 Murphy Xu. 保留所有权利。</p>
            <p>用 ❤️ 和代码构建</p>
        </div>
    </footer>

    <script>
        // 深色模式切换
        const themeToggle = document.getElementById('themeToggle');
        const body = document.body;
        
        // 检查系统偏好
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            body.classList.add('dark-mode');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        }
        
        themeToggle.addEventListener('click', () => {
            body.classList.toggle('dark-mode');
            if (body.classList.contains('dark-mode')) {
                themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            }
        });
        
        // 表单提交
        const contactForm = document.getElementById('contactForm');
        contactForm.addEventListener('submit', (e) => {
            e.preventDefault();
            alert('感谢您的留言！我会尽快回复您。');
            contactForm.reset();
        });
        
        // 滚动动画
        const observerOptions = {
            threshold: 0.1
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                }
            });
        }, observerOptions);
        
        document.querySelectorAll('.skill-progress').forEach(progress => {
            observer.observe(progress);
        });
    </script>
</body>
</html>