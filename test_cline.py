import os
import unittest
import argparse
from unittest.mock import patch, Mock, mock_open
import random
import time
from typing import List, Callable, Tuple


# 判断一个列表是否已经排序
def is_sorted(arr: List[int]) -> bool:
    """判断一个列表是否已经排序"""
    return all(arr[i] <= arr[i + 1] for i in range(len(arr) - 1))


# 冒泡排序
def bubble_sort(arr: List[int]) -> List[int]:
    """冒泡排序"""
    arr = arr.copy()  # 创建副本，避免修改原数组
    n = len(arr)
    for i in range(n - 1):
        swapped = False
        for j in range(0, n - i - 1):
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
                swapped = True
        if not swapped:  # 如果一轮中没有交换，说明已经排序完成
            break
    return arr

# 插入排序
def insertion_sort(arr: List[int]) -> List[int]:
    """插入排序"""
    arr = arr.copy()  # 创建副本，避免修改原数组
    for i in range(1, len(arr)):
        key = arr[i]
        j = i - 1
        while j >= 0 and key < arr[j]:
            arr[j + 1] = arr[j]
            j -= 1
        arr[j + 1] = key
    return arr

# 快速排序
def quick_sort(arr: List[int]) -> List[int]:
    """快速排序"""
    if len(arr) <= 1:
        return arr.copy()  # 创建副本，避免修改原数组

    # 优化：选择中间元素作为基准点
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quick_sort(left) + middle + quick_sort(right)


def test_basic_sorting() -> None:
    """测试基本排序功能"""
    test_cases = [
        [3, 2, 1],
        [9, 5, 2, 7, 1],
        [],
        [1, 1, 1],
        [5, 4, 3, 2, 1]
    ]

    sorting_algorithms = [
        ("冒泡排序", bubble_sort),
        ("插入排序", insertion_sort),
        ("快速排序", quick_sort)
    ]

    for arr in test_cases:
        print(f"原始数组: {arr}")
        for name, sort_func in sorting_algorithms:
            sorted_arr = sort_func(arr.copy())
            print(f"{name}: {sorted_arr}")
        print()


def test_sorting_performance(sizes: List[int] = None, verbose: bool = False) -> None:
    """测试排序算法性能

    Args:
        sizes: 要测试的数组大小列表
        verbose: 是否显示详细信息
    """
    if sizes is None:
        sizes = [100, 1000, 10000]

    sorting_algorithms = [
        ("冒泡排序", bubble_sort),
        ("插入排序", insertion_sort),
        ("快速排序", quick_sort)
    ]

    results = []  # 存储结果以便最后汇总

    for size in sizes:
        print(f"\n===== 测试数组大小: {size} =====")
        # 创建随机数组
        arr = list(range(size))
        random.shuffle(arr)

        for name, sort_func in sorting_algorithms:
            # 计时
            start_time = time.time()
            sorted_arr = sort_func(arr.copy())
            end_time = time.time()
            duration = end_time - start_time

            # 验证排序结果
            is_correct = is_sorted(sorted_arr) and sorted_arr == sorted(arr)

            # 存储结果
            results.append((name, size, duration, is_correct))

            # 输出结果
            print(f"算法: {name:<10} - 数组大小: {size} - 耗时: {duration:.6f} 秒")

            if verbose:
                print(f"  排序结果正确: {is_correct}")
                if size <= 20:  # 只对小数组显示完整结果
                    print(f"  排序结果: {sorted_arr}")
                else:
                    print(f"  排序结果前10个元素: {sorted_arr[:10]}...")

    # 汇总结果
    print("\n===== 性能汇总 =====")
    for size in sizes:
        print(f"\n数组大小: {size}")
        for name, s, duration, is_correct in [r for r in results if r[1] == size]:
            print(f"  {name:<10}: {duration:.6f} 秒 {'(正确)' if is_correct else '(错误)'}")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="排序算法测试程序")
    parser.add_argument("-b", "--basic", action="store_true", help="运行基本排序测试")
    parser.add_argument("-p", "--performance", action="store_true", help="运行性能测试")
    parser.add_argument("-s", "--sizes", type=int, nargs="+", default=[100, 1000, 10000],
                        help="性能测试的数组大小，例如: -s 100 1000 10000")
    parser.add_argument("-v", "--verbose", action="store_true", help="显示详细输出")

    args = parser.parse_args()

    # 如果没有指定任何测试，则默认运行所有测试
    if not (args.basic or args.performance):
        args.basic = True
        args.performance = True

    return args


def main():
    """主函数"""
    args = parse_arguments()

    if args.basic:
        print("\n===== 基本排序测试 =====")
        test_basic_sorting()

    if args.performance:
        print("\n===== 排序性能测试 =====")
        test_sorting_performance(args.sizes, args.verbose)


if __name__ == '__main__':
    # test_sorting_performance()
    main()

