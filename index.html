<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Podcast App Prototype</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .iphone-frame {
            width: 390px;
            height: 844px;
            border-radius: 55px;
            padding: 20px;
            background: #f5f5f7;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 40px rgba(0,0,0,0.1);
        }
        .screen {
            width: 100%;
            height: 100%;
            border-radius: 35px;
            overflow: hidden;
            background: white;
        }
        .prototype-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(420px, 1fr));
            gap: 40px;
            padding: 40px;
        }
        .screen-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="max-w-7xl mx-auto py-8">
        <h1 class="text-3xl font-bold text-center mb-8">Podcast App Prototype</h1>
        <div class="prototype-container">
            <div>
                <div class="screen-title">Home Screen</div>
                <div class="iphone-frame">
                    <div class="screen">
                        <iframe src="screens/home.html" frameborder="0" width="100%" height="100%"></iframe>
                    </div>
                </div>
            </div>
            <div>
                <div class="screen-title">Subscriptions Screen</div>
                <div class="iphone-frame">
                    <div class="screen">
                        <iframe src="screens/subscriptions.html" frameborder="0" width="100%" height="100%"></iframe>
                    </div>
                </div>
            </div>
            <div>
                <div class="screen-title">Player Screen</div>
                <div class="iphone-frame">
                    <div class="screen">
                        <iframe src="screens/player.html" frameborder="0" width="100%" height="100%"></iframe>
                    </div>
                </div>
            </div>
            <div>
                <div class="screen-title">Profile Screen</div>
                <div class="iphone-frame">
                    <div class="screen">
                        <iframe src="screens/profile.html" frameborder="0" width="100%" height="100%"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 