# Path: test_caina.py
 # -*- coding: utf-8 -*-
import time
import random
import sys

# 生成包含n个随机整数的列表，随机数范围0-100000
def make_random_list(n):
    return [random.randint(0, 100000) for _ in range(n)]
# 冒泡排序算法实现
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

# 插入排序算法实现
def insert_sort(arr):
    for i in range(1, len(arr)):
        key = arr[i]
        j = i-1
        while j >=0 and key < arr[j]:
                arr[j+1] = arr[j]
                j -= 1
        arr[j+1] = key
    return arr

# 快速排序算法实现
def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quick_sort(left) + middle + quick_sort(right)

# 归并排序算法实现
def merge_sort(arr):
    if len(arr) <= 1:
        return arr
    mid = len(arr) // 2
    left = arr[:mid]
    right = arr[mid:]
    return merge(merge_sort(left), merge_sort(right))

# 归并排序的辅助函数，用于合并两个已排序数组
def merge(left, right):
    result = []
    while left and right:
        if left[0] < right[0]:
            result.append(left.pop(0))
        else:
            result.append(right.pop(0))
    result.extend(left)
    result.extend(right)
    return result

# 选择排序算法实现
def select_sort(arr):
    for i in range(len(arr)):
        min_idx = i
        for j in range(i+1, len(arr)):
            if arr[min_idx] > arr[j]:
                min_idx = j
        arr[i], arr[min_idx] = arr[min_idx], arr[i]
    return arr

# 测试各种排序算法在10000个随机数上的性能
def test_sort():
    l = make_random_list(10000)
    start = time.time()
    bubble_sort(l.copy())
    end = time.time()
    print("冒泡排序时间：", end - start)
    l = make_random_list(10000)
    start = time.time()
    insert_sort(l.copy())
    end = time.time()
    print("插入排序时间：", end - start)
    l = make_random_list(10000)
    start = time.time()
    quick_sort(l.copy())
    end = time.time()
    print("快速排序时间：", end - start)
    l = make_random_list(10000)
    start = time.time()
    merge_sort(l.copy())
    end = time.time()
    print("归并排序时间：", end - start)
    l = make_random_list(10000)
    start = time.time()
    select_sort(l.copy())
    end = time.time()
    print("选择排序时间：", end - start)

# 主函数
def main():
    test_sort()


if __name__ == "__main__":
    main()
