<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播客详情 - 播客应用</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .podcast-header {
            position: relative;
            height: 200px;
            overflow: hidden;
        }
        .podcast-header-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            filter: blur(20px);
            opacity: 0.7;
            z-index: 1;
        }
        .podcast-header-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            align-items: center;
            padding: 16px;
        }
        .podcast-cover-large {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .episode-item {
            border-bottom: 1px solid #f0f0f0;
            padding: 16px 0;
        }
        .episode-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- 播客头部 -->
    <div class="podcast-header">
        <div class="podcast-header-bg" style="background-image: url('https://images.unsplash.com/photo-1478737270239-2f02b77fc618?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80');"></div>
        <div class="podcast-header-content">
            <button class="absolute top-4 left-4 text-white">
                <i class="fas fa-arrow-left text-xl"></i>
            </button>
            <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" class="podcast-cover-large" alt="播客封面">
            <div class="ml-4 text-white">
                <h1 class="text-xl font-bold">科技早知道</h1>
                <p class="text-sm opacity-80">36氪旗下科技播客</p>
                <div class="flex items-center mt-2">
                    <i class="fas fa-star text-yellow-400 text-xs"></i>
                    <span class="text-xs ml-1">4.9 (2,345)</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-around py-4 bg-white border-b border-gray-100">
        <button class="btn-primary flex items-center">
            <i class="fas fa-plus mr-2"></i>
            <span>订阅</span>
        </button>
        <button class="btn-outline flex items-center">
            <i class="fas fa-share mr-2"></i>
            <span>分享</span>
        </button>
    </div>

    <!-- 内容区域 -->
    <div class="content pt-0">
        <!-- 播客介绍 -->
        <div class="bg-white p-4 rounded-lg shadow-sm mb-4">
            <h2 class="text-lg font-semibold mb-2">关于播客</h2>
            <p class="text-gray-600 text-sm">《科技早知道》是由36氪推出的科技新闻播客栏目，每天早上为你提供最新最热的科技资讯。我们致力于为你提供有观点、有态度的科技新闻分析，让你用最短的时间获取最有价值的信息。</p>
            <div class="flex flex-wrap mt-3">
                <span class="category-tag">科技</span>
                <span class="category-tag">新闻</span>
                <span class="category-tag">商业</span>
            </div>
        </div>

        <!-- 剧集列表 -->
        <div class="bg-white rounded-lg shadow-sm">
            <div class="p-4 border-b border-gray-100">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold">所有剧集</h2>
                    <div class="flex items-center">
                        <span class="text-sm text-gray-500 mr-2">排序</span>
                        <i class="fas fa-sort text-gray-500"></i>
                    </div>
                </div>
            </div>
            
            <!-- 剧集项 -->
            <div class="episode-item p-4">
                <div class="flex justify-between items-start mb-2">
                    <h3 class="font-semibold">S7E23: 苹果发布会前瞻</h3>
                    <button class="text-gray-500">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
                <p class="text-sm text-gray-600 mb-3">本期节目我们将深入探讨即将到来的苹果秋季发布会，预测可能发布的新品以及对行业的影响。</p>
                <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-500">今天 · 1小时6分钟</span>
                    <div class="flex items-center">
                        <button class="mr-4 text-gray-500">
                            <i class="far fa-heart"></i>
                        </button>
                        <button class="text-gray-500">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="ml-4 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-play text-gray-700 text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="episode-item p-4">
                <div class="flex justify-between items-start mb-2">
                    <h3 class="font-semibold">S7E22: AI大模型最新进展</h3>
                    <button class="text-gray-500">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
                <p class="text-sm text-gray-600 mb-3">本期我们邀请到AI领域专家，讨论大语言模型的最新技术突破和应用场景。</p>
                <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-500">3天前 · 58分钟</span>
                    <div class="flex items-center">
                        <button class="mr-4 text-gray-500">
                            <i class="far fa-heart"></i>
                        </button>
                        <button class="text-gray-500">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="ml-4 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-play text-gray-700 text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="episode-item p-4">
                <div class="flex justify-between items-start mb-2">
                    <h3 class="font-semibold">S7E21: 元宇宙的现状与未来</h3>
                    <button class="text-gray-500">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
                <p class="text-sm text-gray-600 mb-3">元宇宙概念火热了一年多，但实际发展如何？本期节目我们将盘点元宇宙领域的最新进展。</p>
                <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-500">上周 · 1小时12分钟</span>
                    <div class="flex items-center">
                        <button class="mr-4 text-gray-500">
                            <i class="far fa-heart"></i>
                        </button>
                        <button class="text-gray-500">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="ml-4 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-play text-gray-700 text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="episode-item p-4">
                <div class="flex justify-between items-start mb-2">
                    <h3 class="font-semibold">S7E20: 芯片战争最新进展</h3>
                    <button class="text-gray-500">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
                <p class="text-sm text-gray-600 mb-3">全球芯片供应链紧张局势持续，本期我们将分析最新的产业动态和政策影响。</p>
                <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-500">2周前 · 53分钟</span>
                    <div class="flex items-center">
                        <button class="mr-4 text-gray-500">
                            <i class="far fa-heart"></i>
                        </button>
                        <button class="text-gray-500">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="ml-4 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-play text-gray-700 text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 迷你播放器 -->
    <div class="mini-player">
        <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80" class="mini-player-cover" alt="播客封面">
        <div class="mini-player-info">
            <div class="mini-player-title">科技早知道</div>
            <div class="mini-player-author">S7E23: 苹果发布会前瞻</div>
        </div>
        <div class="mini-player-controls">
            <i class="fas fa-play text-gray-700"></i>
            <i class="fas fa-forward text-gray-700"></i>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home tab-icon"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-compass tab-icon"></i>
            <span>发现</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-bookmark tab-icon"></i>
            <span>订阅</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user tab-icon"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>