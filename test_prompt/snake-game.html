<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            overflow: hidden;
            touch-action: none;
        }
        .game-board {
            background-color: #1a202c;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .snake-part {
            position: absolute;
            border-radius: 2px;
            transition: all 0.1s linear;
        }
        .food {
            position: absolute;
            border-radius: 50%;
            transition: all 0.2s ease;
        }
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }
        .food {
            animation: pulse 1s infinite;
        }
        .game-over-overlay {
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-900 to-gray-800 min-h-screen flex flex-col items-center justify-center p-4">
    <div class="max-w-md w-full bg-gray-800 rounded-xl shadow-2xl overflow-hidden">
        <div class="p-4 bg-gray-700 flex justify-between items-center">
            <div class="text-white font-bold text-xl">贪吃蛇游戏</div>
            <div class="flex space-x-4">
                <div class="text-green-400">
                    分数: <span id="score" class="font-bold">0</span>
                </div>
                <div class="text-yellow-400">
                    最高分: <span id="high-score" class="font-bold">0</span>
                </div>
            </div>
        </div>
        
        <div class="p-4 flex justify-center">
            <button id="reset-btn" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-6 rounded-full transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50">
                重新开始
            </button>
        </div>
        
        <div class="p-4 flex justify-center">
            <div id="game-container" class="game-board w-full h-80 rounded-lg">
                <div id="game-over" class="game-over-overlay hidden">
                    <div class="text-white text-3xl font-bold mb-4">游戏结束!</div>
                    <div class="text-green-400 text-xl">最终得分: <span id="final-score">0</span></div>
                    <button id="restart-btn" class="mt-6 bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-full transition-all duration-200 transform hover:scale-105">
                        再玩一次
                    </button>
                </div>
            </div>
        </div>
        
        <div class="p-4 bg-gray-700 text-center text-gray-300 text-sm">
            使用键盘方向键或下方按钮控制蛇的移动
        </div>
        
        <!-- 移动控制按钮 (适用于移动设备) -->
        <div class="p-4 grid grid-cols-3 gap-2">
            <div></div>
            <button id="up-btn" class="bg-gray-600 hover:bg-gray-500 text-white p-3 rounded-lg flex justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                </svg>
            </button>
            <div></div>
            <button id="left-btn" class="bg-gray-600 hover:bg-gray-500 text-white p-3 rounded-lg flex justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </button>
            <div></div>
            <button id="right-btn" class="bg-gray-600 hover:bg-gray-500 text-white p-3 rounded-lg flex justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
            <div></div>
            <button id="down-btn" class="bg-gray-600 hover:bg-gray-500 text-white p-3 rounded-lg flex justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
            </button>
            <div></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // 游戏配置
            const config = {
                gridSize: 20,
                initialSpeed: 150,
                speedIncrease: 5,
                colors: {
                    snakeHead: '#4C1D95',
                    snakeBody: '#6D28D9',
                    food: '#EF4444'
                }
            };

            // 游戏状态
            let gameState = {
                snake: [{ x: 10, y: 10 }],
                food: { x: 5, y: 5 },
                direction: 'right',
                nextDirection: 'right',
                score: 0,
                highScore: localStorage.getItem('snakeHighScore') || 0,
                gameOver: false,
                gameLoop: null,
                speed: config.initialSpeed
            };

            // DOM 元素
            const gameContainer = document.getElementById('game-container');
            const scoreElement = document.getElementById('score');
            const highScoreElement = document.getElementById('high-score');
            const gameOverElement = document.getElementById('game-over');
            const finalScoreElement = document.getElementById('final-score');
            const resetButton = document.getElementById('reset-btn');
            const restartButton = document.getElementById('restart-btn');
            
            // 方向按钮
            const upButton = document.getElementById('up-btn');
            const leftButton = document.getElementById('left-btn');
            const rightButton = document.getElementById('right-btn');
            const downButton = document.getElementById('down-btn');

            // 初始化游戏
            function initGame() {
                // 清除之前的游戏元素
                gameContainer.querySelectorAll('.snake-part, .food').forEach(el => el.remove());
                
                // 重置游戏状态
                gameState = {
                    snake: [{ x: 10, y: 10 }],
                    food: generateFoodPosition(),
                    direction: 'right',
                    nextDirection: 'right',
                    score: 0,
                    highScore: localStorage.getItem('snakeHighScore') || 0,
                    gameOver: false,
                    gameLoop: null,
                    speed: config.initialSpeed
                };
                
                // 更新分数显示
                scoreElement.textContent = gameState.score;
                highScoreElement.textContent = gameState.highScore;
                
                // 隐藏游戏结束界面
                gameOverElement.classList.add('hidden');
                
                // 创建蛇和食物
                createSnake();
                createFood();
                
                // 开始游戏循环
                if (gameState.gameLoop) clearInterval(gameState.gameLoop);
                gameState.gameLoop = setInterval(gameStep, gameState.speed);
            }

            // 生成食物位置
            function generateFoodPosition() {
                let position;
                let overlap;
                
                do {
                    overlap = false;
                    position = {
                        x: Math.floor(Math.random() * config.gridSize),
                        y: Math.floor(Math.random() * config.gridSize)
                    };
                    
                    // 检查是否与蛇身重叠
                    for (const part of gameState.snake) {
                        if (part.x === position.x && part.y === position.y) {
                            overlap = true;
                            break;
                        }
                    }
                } while (overlap);
                
                return position;
            }

            // 创建蛇
            function createSnake() {
                gameState.snake.forEach((part, index) => {
                    const snakePart = document.createElement('div');
                    snakePart.className = 'snake-part';
                    snakePart.style.width = `${100 / config.gridSize}%`;
                    snakePart.style.height = `${100 / config.gridSize}%`;
                    snakePart.style.left = `${part.x * (100 / config.gridSize)}%`;
                    snakePart.style.top = `${part.y * (100 / config.gridSize)}%`;
                    
                    // 蛇头与身体使用不同颜色
                    snakePart.style.backgroundColor = index === 0 ? config.colors.snakeHead : config.colors.snakeBody;
                    
                    // 添加数据属性以便于更新
                    snakePart.dataset.index = index;
                    
                    gameContainer.appendChild(snakePart);
                });
            }

            // 创建食物
            function createFood() {
                const food = document.createElement('div');
                food.className = 'food';
                food.style.width = `${100 / config.gridSize}%`;
                food.style.height = `${100 / config.gridSize}%`;
                food.style.left = `${gameState.food.x * (100 / config.gridSize)}%`;
                food.style.top = `${gameState.food.y * (100 / config.gridSize)}%`;
                food.style.backgroundColor = config.colors.food;
                
                gameContainer.appendChild(food);
            }

            // 更新蛇的位置
            function updateSnake() {
                // 移除旧的蛇元素
                gameContainer.querySelectorAll('.snake-part').forEach(el => el.remove());
                
                // 创建新的蛇元素
                createSnake();
            }

            // 更新食物位置
            function updateFood() {
                const foodElement = gameContainer.querySelector('.food');
                if (foodElement) {
                    foodElement.style.left = `${gameState.food.x * (100 / config.gridSize)}%`;
                    foodElement.style.top = `${gameState.food.y * (100 / config.gridSize)}%`;
                } else {
                    createFood();
                }
            }

            // 游戏步骤
            function gameStep() {
                if (gameState.gameOver) return;
                
                // 更新方向
                gameState.direction = gameState.nextDirection;
                
                // 获取蛇头
                const head = { ...gameState.snake[0] };
                
                // 根据方向移动蛇头
                switch (gameState.direction) {
                    case 'up':
                        head.y--;
                        break;
                    case 'down':
                        head.y++;
                        break;
                    case 'left':
                        head.x--;
                        break;
                    case 'right':
                        head.x++;
                        break;
                }
                
                // 检查碰撞
                if (checkCollision(head)) {
                    endGame();
                    return;
                }
                
                // 将新头部添加到蛇身
                gameState.snake.unshift(head);
                
                // 检查是否吃到食物
                if (head.x === gameState.food.x && head.y === gameState.food.y) {
                    // 增加分数
                    gameState.score++;
                    scoreElement.textContent = gameState.score;
                    
                    // 生成新食物
                    gameState.food = generateFoodPosition();
                    updateFood();
                    
                    // 增加速度
                    if (gameState.speed > 50) {
                        gameState.speed -= config.speedIncrease;
                        clearInterval(gameState.gameLoop);
                        gameState.gameLoop = setInterval(gameStep, gameState.speed);
                    }
                } else {
                    // 如果没吃到食物，移除尾部
                    gameState.snake.pop();
                }
                
                // 更新蛇的显示
                updateSnake();
            }

            // 检查碰撞
            function checkCollision(head) {
                // 检查是否撞墙
                if (head.x < 0 || head.x >= config.gridSize || head.y < 0 || head.y >= config.gridSize) {
                    return true;
                }
                
                // 检查是否撞到自己
                for (let i = 0; i < gameState.snake.length; i++) {
                    if (gameState.snake[i].x === head.x && gameState.snake[i].y === head.y) {
                        return true;
                    }
                }
                
                return false;
            }

            // 结束游戏
            function endGame() {
                gameState.gameOver = true;
                clearInterval(gameState.gameLoop);
                
                // 更新最高分
                if (gameState.score > gameState.highScore) {
                    gameState.highScore = gameState.score;
                    localStorage.setItem('snakeHighScore', gameState.highScore);
                    highScoreElement.textContent = gameState.highScore;
                }
                
                // 显示游戏结束界面
                finalScoreElement.textContent = gameState.score;
                gameOverElement.classList.remove('hidden');
            }

            // 键盘控制
            document.addEventListener('keydown', (e) => {
                switch (e.key) {
                    case 'ArrowUp':
                        if (gameState.direction !== 'down') {
                            gameState.nextDirection = 'up';
                        }
                        e.preventDefault();
                        break;
                    case 'ArrowDown':
                        if (gameState.direction !== 'up') {
                            gameState.nextDirection = 'down';
                        }
                        e.preventDefault();
                        break;
                    case 'ArrowLeft':
                        if (gameState.direction !== 'right') {
                            gameState.nextDirection = 'left';
                        }
                        e.preventDefault();
                        break;
                    case 'ArrowRight':
                        if (gameState.direction !== 'left') {
                            gameState.nextDirection = 'right';
                        }
                        e.preventDefault();
                        break;
                }
            });

            // 按钮控制
            upButton.addEventListener('click', () => {
                if (gameState.direction !== 'down') {
                    gameState.nextDirection = 'up';
                }
            });
            
            downButton.addEventListener('click', () => {
                if (gameState.direction !== 'up') {
                    gameState.nextDirection = 'down';
                }
            });
            
            leftButton.addEventListener('click', () => {
                if (gameState.direction !== 'right') {
                    gameState.nextDirection = 'left';
                }
            });
            
            rightButton.addEventListener('click', () => {
                if (gameState.direction !== 'left') {
                    gameState.nextDirection = 'right';
                }
            });

            // 重置按钮
            resetButton.addEventListener('click', initGame);
            restartButton.addEventListener('click', initGame);

            // 初始化游戏
            highScoreElement.textContent = gameState.highScore;
            initGame();
        });
    </script>
</body>
</html>