<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播放器 - 播客应用</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background-color: #f8f8f8;
        }
        .player-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .player-header {
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .player-cover {
            width: 280px;
            height: 280px;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin: 0 auto;
        }
        .player-info {
            padding: 24px 20px;
            text-align: center;
        }
        .player-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .player-author {
            font-size: 16px;
            color: #666;
            margin-bottom: 16px;
        }
        .player-actions {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #666;
        }
        .action-btn i {
            font-size: 24px;
            margin-bottom: 6px;
        }
        .action-btn span {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <div class="player-container">
        <!-- 播放器头部 -->
        <div class="player-header">
            <button class="text-gray-700">
                <i class="fas fa-chevron-down text-xl"></i>
            </button>
            <div class="text-center flex-1">
                <p class="text-sm text-gray-500">正在播放</p>
            </div>
            <button class="text-gray-700">
                <i class="fas fa-ellipsis-h text-xl"></i>
            </button>
        </div>

        <!-- 播放器封面 -->
        <div class="flex-1 flex flex-col justify-center px-6">
            <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" class="player-cover" alt="播客封面">
            
            <!-- 播放器信息 -->
            <div class="player-info">
                <h2 class="player-title">S7E23: 苹果发布会前瞻</h2>
                <p class="player-author">科技早知道</p>
                
                <!-- 进度条 -->
                <div class="mt-6">
                    <div class="progress-bar">
                        <div class="progress" style="width: 35%;"></div>
                        <div class="progress-handle" style="left: 35%;"></div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-500 mt-2">
                        <span>23:15</span>
                        <span>1:06:42</span>
                    </div>
                </div>
                
                <!-- 播放控制 -->
                <div class="player-controls">
                    <button class="control-btn">
                        <i class="fas fa-backward-step text-2xl"></i>
                    </button>
                    <button class="control-btn">
                        <i class="fas fa-backward text-2xl"></i>
                    </button>
                    <button class="play-btn">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button class="control-btn">
                        <i class="fas fa-forward text-2xl"></i>
                    </button>
                    <button class="control-btn">
                        <i class="fas fa-forward-step text-2xl"></i>
                    </button>
                </div>
                
                <!-- 播放速度 -->
                <div class="flex justify-center mt-4">
                    <button class="px-3 py-1 bg-gray-100 rounded-full text-sm font-medium">
                        1.0x
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 播放器操作 -->
        <div class="player-actions px-6 pb-8">
            <div class="action-btn">
                <i class="far fa-clock"></i>
                <span>定时</span>
            </div>
            <div class="action-btn">
                <i class="fas fa-list-ul"></i>
                <span>播放列表</span>
            </div>
            <div class="action-btn">
                <i class="far fa-heart"></i>
                <span>收藏</span>
            </div>
            <div class="action-btn">
                <i class="fas fa-download"></i>
                <span>下载</span>
            </div>
            <div class="action-btn">
                <i class="fas fa-share-alt"></i>
                <span>分享</span>
            </div>
        </div>
    </div>
</body>
</html>