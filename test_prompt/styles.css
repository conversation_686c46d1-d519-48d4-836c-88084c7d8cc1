/* 通用样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f8f8f8;
    color: #333;
    -webkit-font-smoothing: antialiased;
}

/* 状态栏样式 */
.status-bar {
    height: 44px;
    background-color: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    position: sticky;
    top: 0;
    z-index: 50;
}

.status-bar-time {
    font-weight: 600;
}

.status-bar-icons {
    display: flex;
    gap: 5px;
}

/* 底部导航栏 */
.tab-bar {
    height: 83px;
    background-color: rgba(255, 255, 255, 0.95);
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 40;
    padding-bottom: 20px; /* 为iPhone底部安全区域留出空间 */
}

.tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #8e8e93;
    font-size: 10px;
    padding: 5px 0;
}

.tab-item.active {
    color: #ff7846; /* 小宇宙的主题色 */
}

.tab-icon {
    font-size: 22px;
    margin-bottom: 2px;
}

/* 内容区域 */
.content {
    padding: 16px;
    padding-bottom: 100px; /* 为底部导航栏留出空间 */
    overflow-y: auto;
    height: calc(100vh - 44px - 83px);
}

/* 卡片样式 */
.podcast-card {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.podcast-cover {
    width: 100%;
    aspect-ratio: 1/1;
    object-fit: cover;
    border-radius: 8px;
}

/* 迷你播放器 */
.mini-player {
    position: fixed;
    bottom: 83px;
    left: 0;
    right: 0;
    height: 60px;
    background-color: white;
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 30;
}

.mini-player-cover {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    margin-right: 12px;
}

.mini-player-info {
    flex: 1;
}

.mini-player-title {
    font-weight: 600;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mini-player-author {
    font-size: 12px;
    color: #8e8e93;
}

.mini-player-controls {
    display: flex;
    gap: 20px;
}

/* 按钮样式 */
.btn-primary {
    background-color: #ff7846;
    color: white;
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: 600;
    border: none;
}

.btn-outline {
    background-color: transparent;
    color: #ff7846;
    border: 1px solid #ff7846;
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: 600;
}

/* 分类标签 */
.category-tag {
    background-color: #f2f2f7;
    border-radius: 16px;
    padding: 6px 12px;
    font-size: 13px;
    margin-right: 8px;
    margin-bottom: 8px;
    display: inline-block;
}

.category-tag.active {
    background-color: #ff7846;
    color: white;
}

/* 播放器进度条 */
.progress-bar {
    width: 100%;
    height: 4px;
    background-color: #e0e0e0;
    border-radius: 2px;
    position: relative;
    margin: 16px 0;
}

.progress {
    height: 100%;
    background-color: #ff7846;
    border-radius: 2px;
}

.progress-handle {
    width: 12px;
    height: 12px;
    background-color: #ff7846;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

/* 搜索框 */
.search-bar {
    background-color: #f2f2f7;
    border-radius: 10px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.search-bar input {
    border: none;
    background: transparent;
    flex: 1;
    margin-left: 8px;
    font-size: 16px;
}

.search-bar input:focus {
    outline: none;
}

/* 分割线 */
.divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 16px 0;
}

/* 列表项 */
.list-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f2f2f7;
}

.list-item-content {
    flex: 1;
}

/* 头像 */
.avatar {
    width: 60px;
    height: 60px;
    border-radius: 30px;
    object-fit: cover;
}

/* 设置项 */
.settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f2f2f7;
}

/* 播放器控制按钮 */
.player-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
}

.control-btn {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20px;
}

.play-btn {
    width: 60px;
    height: 60px;
    background-color: #ff7846;
    color: white;
    border-radius: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
}