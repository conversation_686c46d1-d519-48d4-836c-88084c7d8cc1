<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置 - 播客应用</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- 头部 -->
    <div class="flex items-center p-4 border-b border-gray-200">
        <button class="mr-4">
            <i class="fas fa-arrow-left text-xl"></i>
        </button>
        <h1 class="text-xl font-bold">设置</h1>
    </div>

    <!-- 内容区域 -->
    <div class="content pt-0">
        <!-- 账户设置 -->
        <div class="bg-white rounded-lg shadow-sm mb-6">
            <div class="p-4 border-b border-gray-100">
                <h2 class="font-semibold">账户设置</h2>
            </div>
            <div class="settings-item px-4">
                <div>
                    <p class="font-medium">个人资料</p>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
            <div class="settings-item px-4">
                <div>
                    <p class="font-medium">通知设置</p>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
            <div class="settings-item px-4">
                <div>
                    <p class="font-medium">隐私与安全</p>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
        </div>

        <!-- 播放设置 -->
        <div class="bg-white rounded-lg shadow-sm mb-6">
            <div class="p-4 border-b border-gray-100">
                <h2 class="font-semibold">播放设置</h2>
            </div>
            <div class="settings-item px-4">
                <div>
                    <p class="font-medium">音质设置</p>
                    <p class="text-sm text-gray-500">标准</p>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
            <div class="settings-item px-4">
                <div>
                    <p class="font-medium">自动播放</p>
                </div>
                <div class="relative inline-block w-10 mr-2 align-middle select-none">
                    <input type="checkbox" name="toggle" id="autoplay" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" checked/>
                    <label for="autoplay" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                </div>
                <style>
                    .toggle-checkbox:checked {
                        right: 0;
                        border-color: #ff7846;
                    }
                    .toggle-checkbox:checked + .toggle-label {
                        background-color: #ff7846;
                    }
                </style>
            </div>
            <div class="settings-item px-4">
                <div>
                    <p class="font-medium">下载设置</p>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
        </div>

        <!-- 存储与缓存 -->
        <div class="bg-white rounded-lg shadow-sm mb-6">
            <div class="p-4 border-b border-gray-100">
                <h2 class="font-semibold">存储与缓存</h2>
            </div>
            <div class="settings-item px-4">
                <div>
                    <p class="font-medium">清除缓存</p>
                    <p class="text-sm text-gray-500">当前缓存: 256MB</p>
                </div>
                <button class="text-blue-500 text-sm font-medium">清除</button>
            </div>
            <div class="settings-item px-4">
                <div>
                    <p class="font-medium">存储位置</p>
                    <p class="text-sm text-gray-500">内部存储</p>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
        </div>

        <!-- 关于 -->
        <div class="bg-white rounded-lg shadow-sm mb-6">
            <div class="p-4 border-b border-gray-100">
                <h2 class="font-semibold">关于</h2>
            </div>
            <div class="settings-item px-4">
                <div>
                    <p class="font-medium">版本信息</p>
                    <p class="text-sm text-gray-500">v1.0.0</p>
                </div>
            </div>
            <div class="settings-item px-4">
                <div>
                    <p class="font-medium">用户协议</p>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
            <div class="settings-item px-4">
                <div>
                    <p class="font-medium">隐私政策</p>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
            <div class="settings-item px-4">
                <div>
                    <p class="font-medium">反馈与建议</p>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
        </div>

        <!-- 退出登录 -->
        <button class="w-full py-3 text-center text-red-500 font-medium bg-white rounded-lg shadow-sm">
            退出登录
        </button>
    </div>
</body>
</html>