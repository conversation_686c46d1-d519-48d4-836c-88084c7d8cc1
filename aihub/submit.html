<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit a Product - AI Hub</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css">
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100">

    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-md">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <a href="/" class="text-2xl font-bold text-gray-800 dark:text-white">AI Hub</a>
            <div class="flex items-center">
                <a href="index.html" class="text-gray-600 dark:text-gray-300 mx-4">Home</a>
                <button id="theme-toggle" class="text-gray-600 dark:text-gray-300 focus:outline-none">
                    <!-- Icons will be handled by JS -->
                </button>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-8">
            <h1 class="text-3xl font-bold mb-6">Submit a Product</h1>
            <!-- Multi-step form will go here -->
            <form id="submit-form">
                <!-- Step 1: Product Info -->
                <div id="step-1">
                    <div class="mb-4">
                        <label for="product-name" class="block text-sm font-medium mb-1">Product Name</label>
                        <input type="text" id="product-name" class="w-full px-4 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600" required>
                    </div>
                    <div class="mb-4">
                        <label for="product-tagline" class="block text-sm font-medium mb-1">Tagline</label>
                        <input type="text" id="product-tagline" class="w-full px-4 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600" required>
                    </div>
                    <div class="mb-4">
                        <label for="product-icon" class="block text-sm font-medium mb-1">Icon URL</label>
                        <input type="url" id="product-icon" class="w-full px-4 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600" required>
                    </div>
                    <button type="button" id="next-step-1" class="bg-indigo-600 text-white px-6 py-2 rounded-lg">Next</button>
                </div>

                <!-- Step 2: Details -->
                <div id="step-2" class="hidden">
                    <div class="mb-4">
                        <label for="product-tags" class="block text-sm font-medium mb-1">Tags (comma-separated)</label>
                        <input type="text" id="product-tags" class="w-full px-4 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                    </div>
                    <div class="mb-4">
                        <label for="product-description" class="block text-sm font-medium mb-1">Description</label>
                        <textarea id="product-description" rows="4" class="w-full px-4 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600"></textarea>
                    </div>
                    <button type="button" id="prev-step-2" class="bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-white px-6 py-2 rounded-lg">Back</button>
                    <button type="button" id="next-step-2" class="bg-indigo-600 text-white px-6 py-2 rounded-lg">Preview</button>
                </div>

                <!-- Step 3: Preview -->
                <div id="step-3" class="hidden">
                    <h2 class="text-2xl font-bold mb-4">Preview</h2>
                    <div id="preview-card"></div>
                    <button type="button" id="prev-step-3" class="bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-white px-6 py-2 rounded-lg">Back</button>
                    <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded-lg">Submit</button>
                </div>
            </form>
        </div>
    </main>

    <script src="submit.js"></script>
</body>
</html>