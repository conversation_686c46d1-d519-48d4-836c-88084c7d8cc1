document.addEventListener('DOMContentLoaded', () => {
    const productDetailContainer = document.getElementById('product-detail-container');
    let allProducts = [];
    let currentProduct = null;

    // Mock comments for demonstration
    const mockComments = {
        1: [
            { user: 'Alice', text: 'This is amazing! Changed my workflow completely.' },
            { user: 'Bob', text: 'A bit buggy, but has potential.' },
        ],
        2: [
            { user: '<PERSON>', text: 'Super helpful for marketing campaigns.' },
        ]
    };

    function loadProducts() {
        const initialMockProducts = [
            {
                id: 1,
            name: 'AI-Powered Code Assistant',
            tagline: 'Your personal AI pair programmer.',
            upvotes: 120,
            comments: 15,
            tags: ['Developer Tools', 'AI'],
                icon: 'https://picsum.photos/seed/1/80/80',
            description: 'A powerful AI assistant that helps you write better code, faster. It provides real-time suggestions, completions, and bug fixes.'
        },
        {
            id: 2,
            name: 'Content Genie',
            tagline: 'Generate high-quality marketing copy in seconds.',
            upvotes: 98,
            comments: 22,
            tags: ['Marketing', 'Writing'],
                icon: 'https://picsum.photos/seed/2/80/80',
            description: 'Use AI to craft compelling ad copy, blog posts, and social media updates. Content Genie is your secret weapon for content creation.'
        },
            {
                id: 3,
                name: 'Designify',
                tagline: 'Create stunning designs with AI.',
                upvotes: 210,
                comments: 30,
                tags: ['Design', 'Art'],
                icon: 'https://picsum.photos/seed/3/80/80'
            },
            {
                id: 4,
                name: 'LegalEagle AI',
                tagline: 'Simplify your legal research.',
                upvotes: 75,
                comments: 8,
                tags: ['Legal', 'Research'],
                icon: 'https://picsum.photos/seed/4/80/80'
            },
            {
                id: 5,
                name: 'HealthBot',
                tagline: 'Your AI-powered health companion.',
                upvotes: 150,
                comments: 45,
                tags: ['Health', 'Lifestyle'],
                icon: 'https://picsum.photos/seed/5/80/80'
            },
            {
                id: 6,
                name: 'MusicMuse',
                tagline: 'Compose original music with AI.',
                upvotes: 88,
                comments: 18,
                tags: ['Music', 'Creative'],
                icon: 'https://picsum.photos/seed/6/80/80'
            },
            {
                id: 7,
                name: 'FinanceFlow',
                tagline: 'Automate your financial planning.',
                upvotes: 132,
                comments: 25,
                tags: ['Finance', 'Productivity'],
                icon: 'https://picsum.photos/seed/7/80/80'
            },
            {
                id: 8,
                name: 'TravelWise',
                tagline: 'Plan your next trip with AI insights.',
                upvotes: 64,
                comments: 12,
                tags: ['Travel', 'Planning'],
                icon: 'https://picsum.photos/seed/8/80/80'
            },
    ];
        const storedProducts = JSON.parse(localStorage.getItem('mockProducts')) || [];
        const productMap = new Map();
        initialMockProducts.forEach(p => productMap.set(p.id, p));
        storedProducts.forEach(p => productMap.set(p.id, p));
        allProducts = Array.from(productMap.values());
    }

    function renderProductDetails() {
        if (!currentProduct) {
            productDetailContainer.innerHTML = '<p class="text-center">Product not found.</p>';
            return;
        }

        const comments = mockComments[currentProduct.id] || [];

        const detailHTML = `
            <div class="max-w-4xl mx-auto">
                <!-- Product Header -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 flex items-start">
                    <img src="${currentProduct.icon}" alt="${currentProduct.name} icon" class="w-24 h-24 mr-6 rounded-lg">
                    <div class="flex-1">
                        <h1 class="text-4xl font-bold">${currentProduct.name}</h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-2 text-lg">${currentProduct.tagline}</p>
                        <div class="mt-4">${currentProduct.tags ? currentProduct.tags.map(tag => `<span class="bg-gray-200 dark:bg-gray-700 text-sm font-medium mr-2 px-2.5 py-0.5 rounded">${tag}</span>`).join('') : ''}</div>
                    </div>
                    <div class="text-center ml-4">
                        <button class="border border-gray-300 dark:border-gray-600 rounded-lg px-6 py-3 flex flex-col items-center hover:bg-gray-100 dark:hover:bg-gray-700">
                            <span class="text-3xl">⬆️</span>
                            <span class="font-bold text-xl">${currentProduct.upvotes}</span>
                        </button>
                    </div>
                </div>

                <!-- Product Description -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mt-8">
                    <h2 class="text-2xl font-bold mb-4">About this product</h2>
                    <p class="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">${currentProduct.description || 'No description available.'}</p>
                </div>

                <!-- Comments Section -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mt-8">
                    <h2 class="text-2xl font-bold mb-4">Discussion</h2>
                    <div id="comment-list">
                        ${comments.map(comment => `
                            <div class="border-b dark:border-gray-700 py-4">
                                <p class="font-semibold">${comment.user}</p>
                                <p class="text-gray-600 dark:text-gray-400">${comment.text}</p>
                            </div>
                        `).join('')}
                    </div>
                    <form id="comment-form" class="mt-6">
                        <textarea id="comment-text" class="w-full px-4 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600" rows="3" placeholder="Add a comment..." required></textarea>
                        <button type="submit" class="bg-indigo-600 text-white px-6 py-2 rounded-lg mt-2">Submit Comment</button>
                    </form>
                </div>
            </div>
        `;
        productDetailContainer.innerHTML = detailHTML;

        // Add event listener for the new comment form
        document.getElementById('comment-form').addEventListener('submit', handleCommentSubmit);
    }

    function handleCommentSubmit(e) {
        e.preventDefault();
        const commentText = document.getElementById('comment-text').value;
        if (!commentText.trim()) return;

        const newComment = {
            user: 'Guest',
            text: commentText
        };

        const commentList = document.getElementById('comment-list');
        const commentHTML = `
            <div class="border-b dark:border-gray-700 py-4">
                <p class="font-semibold">${newComment.user}</p>
                <p class="text-gray-600 dark:text-gray-400">${newComment.text}</p>
            </div>
        `;
        commentList.insertAdjacentHTML('beforeend', commentHTML);

        console.log('New comment for product', currentProduct.id, newComment);

        document.getElementById('comment-text').value = '';
    }

    const urlParams = new URLSearchParams(window.location.search);
    const productId = parseInt(urlParams.get('id'), 10);

    loadProducts();
    currentProduct = allProducts.find(p => p.id === productId);
    renderProductDetails();
});