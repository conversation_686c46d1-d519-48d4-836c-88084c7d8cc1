document.addEventListener('DOMContentLoaded', () => {
    const step1 = document.getElementById('step-1');
    const step2 = document.getElementById('step-2');
    const step3 = document.getElementById('step-3');

    const nextStep1 = document.getElementById('next-step-1');
    const prevStep2 = document.getElementById('prev-step-2');
    const nextStep2 = document.getElementById('next-step-2');
    const prevStep3 = document.getElementById('prev-step-3');

    const form = document.getElementById('submit-form');
    const previewCard = document.getElementById('preview-card');

    nextStep1.addEventListener('click', () => {
        // Basic validation
        const name = document.getElementById('product-name').value;
        const tagline = document.getElementById('product-tagline').value;
        const icon = document.getElementById('product-icon').value;
        if (name && tagline && icon) {
            step1.classList.add('hidden');
            step2.classList.remove('hidden');
        } else {
            alert('Please fill in all fields.');
        }
    });

    prevStep2.addEventListener('click', () => {
        step2.classList.add('hidden');
        step1.classList.remove('hidden');
    });

    nextStep2.addEventListener('click', () => {
        generatePreview();
        step2.classList.add('hidden');
        step3.classList.remove('hidden');
    });

    prevStep3.addEventListener('click', () => {
        step3.classList.add('hidden');
        step2.classList.remove('hidden');
    });

    form.addEventListener('submit', (e) => {
        e.preventDefault();
        // In a real app, you'd send this to a server.
        // For this demo, we'll save to localStorage.
        const newProduct = {
            id: Date.now(),
            name: document.getElementById('product-name').value,
            tagline: document.getElementById('product-tagline').value,
            icon: document.getElementById('product-icon').value,
            tags: document.getElementById('product-tags').value.split(',').map(t => t.trim()),
            description: document.getElementById('product-description').value,
            upvotes: 0,
            comments: 0,
        };

        // Retrieve existing products, add the new one, and save back.
        const products = JSON.parse(localStorage.getItem('mockProducts') || '[]');
        products.push(newProduct);
        localStorage.setItem('mockProducts', JSON.stringify(products));

        alert('Product submitted successfully!');
        window.location.href = 'index.html';
    });

    function generatePreview() {
        const name = document.getElementById('product-name').value;
        const tagline = document.getElementById('product-tagline').value;
        const icon = document.getElementById('product-icon').value;
        const tags = document.getElementById('product-tags').value;

        previewCard.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div class="flex items-start">
                    <img src="${icon}" alt="${name} icon" class="w-20 h-20 mr-6 rounded-lg">
                    <div class="flex-1">
                        <h3 class="text-xl font-bold">${name}</h3>
                        <p class="text-gray-600 dark:text-gray-400 mt-1">${tagline}</p>
                        <div class="flex items-center mt-4 text-sm text-gray-500 dark:text-gray-400">
                            <span class="mr-4">${tags}</span>
                        </div>
                    </div>
                    <div class="text-center ml-4">
                        <button class="border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 flex flex-col items-center">
                            <span class="text-2xl">⬆️</span>
                            <span class="font-bold text-lg">0</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
});