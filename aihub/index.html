<!DOCTYPE html>
<html lang="en" class="">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Hub - Discover, launch and discuss the best AI products</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <script src="i18n.js"></script>
    <script>
        // Set theme on initial load
        if (localStorage.getItem('darkMode') === 'true' || (!('darkMode' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    </script>
</head>
<body class="bg-white dark:bg-black text-gray-900 dark:text-gray-100 font-sans">

    <!-- Header -->
    <header class="bg-white dark:bg-black/80 backdrop-blur-sm sticky top-0 z-40 border-b border-gray-200 dark:border-gray-800">
        <nav class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center space-x-8">
                    <a href="/" class="flex-shrink-0">
                        <h1 class="text-2xl font-bold">
                            <span class="text-indigo-600">AI</span> Hub
                        </h1>
                    </a>
                    <div class="hidden sm:flex items-center space-x-6">
                        <a href="#" class="text-sm font-medium hover:text-indigo-600" data-i18n="nav_trending">Trending</a>
                        <a href="#" class="text-sm font-medium hover:text-indigo-600" data-i18n="nav_new">New</a>
                        <a href="submit.html" class="text-sm font-medium hover:text-indigo-600" data-i18n="nav_submit">Submit</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="hidden md:block">
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="fas fa-search text-gray-400"></i>
                            </span>
                            <input type="search" placeholder="Search AI products..." data-i18n="search_placeholder" class="w-full pl-10 pr-4 py-2 rounded-md border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                    </div>
                    <button id="lang-switcher" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 focus:outline-none">
                        <i class="fas fa-language text-xl"></i>
                    </button>
                    <button id="theme-toggle" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 focus:outline-none">
                        <i class="fas fa-sun hidden dark:inline"></i>
                        <i class="fas fa-moon inline dark:hidden"></i>
                    </button>
                    <a href="#" class="hidden sm:inline-block text-sm font-medium hover:text-indigo-600" data-i18n="nav_login">Login</a>
                    <a href="submit.html" class="hidden sm:inline-block bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                        <i class="fas fa-plus mr-1"></i> <span data-i18n="nav_submit_product">Submit Product</span>
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">

        <!-- Hero Section -->
        <section class="text-center my-12">
            <span class="inline-block bg-indigo-100 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-300 text-sm font-medium px-3 py-1 rounded-full mb-4" data-i18n="hero_pre_title">The community-driven AI product platform</span>
            <h2 class="text-4xl sm:text-5xl lg:text-6xl font-extrabold leading-loose">
                <span data-i18n="hero_title_1">Discover, launch and discuss the</span> <br>
                <span data-i18n="hero_title_2">best</span>
                <span class="text-indigo-600" data-i18n="hero_title_3">AI products</span>
            </h2>
            <p class="mt-4 max-w-2xl mx-auto text-lg text-gray-600 dark:text-gray-400" data-i18n="hero_subtitle">Join the community of makers and users exploring the rapidly evolving AI ecosystem. Find tools that solve real problems.</p>
            <div class="mt-8 flex justify-center">
                 <div class="relative w-full max-w-md">
                    <span class="absolute inset-y-0 left-0 flex items-center pl-4">
                        <i class="fas fa-search text-gray-400"></i>
                    </span>
                    <input type="search" id="hero-search" placeholder="Search AI tools..." data-i18n="hero_search_placeholder" class="w-full pl-12 pr-32 py-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <button class="absolute inset-y-0 right-0 flex items-center px-6 bg-indigo-600 text-white rounded-r-lg hover:bg-indigo-700" data-i18n="hero_search_button">Submit</button>
                </div>
            </div>
            <div class="mt-6 flex justify-center items-center space-x-2 sm:space-x-4 text-sm text-gray-600 dark:text-gray-400">
                <span class="font-medium" data-i18n="popular_categories">Popular categories:</span>
                <a href="#" class="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700" data-i18n="cat_writing">Writing</a>
                <a href="#" class="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700" data-i18n="cat_image_generation">Image Generation</a>
                <a href="#" class="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700" data-i18n="cat_productivity">Productivity</a>
            </div>
        </section>

        <!-- Featured Collections -->
        <section class="my-16">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold" data-i18n="featured_collections">Featured Collections</h3>
                <a href="#" class="text-sm font-medium text-indigo-600 hover:underline" data-i18n="view_all">View all &rarr;</a>
            </div>
            <div id="collections-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Collection cards will be injected here -->
            </div>
        </section>

        <!-- Trending Products -->
        <section class="my-16">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold" data-i18n="trending_products">Trending Products</h3>
                <a href="#" class="text-sm font-medium text-indigo-600 hover:underline" data-i18n="view_all">View all &rarr;</a>
            </div>
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Filters Sidebar -->
                <aside class="w-full lg:w-1/4">
                    <div class="sticky top-24">
                        <div class="border border-gray-200 dark:border-gray-800 rounded-lg p-4">
                            <div class="flex justify-between items-center">
                                <h4 class="font-semibold" data-i18n="filters_title">Filters</h4>
                                <button class="text-sm text-indigo-600 hover:underline" data-i18n="filters_clear">Clear</button>
                            </div>
                            <div class="mt-4">
                                <label for="sort-by" class="sr-only" data-i18n="sort_by">Sort by</label>
                                <select id="sort-by" class="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800">
                                    <option data-i18n="sort_popular_today">Popular Today</option>
                                    <option data-i18n="sort_newest">Newest</option>
                                    <option data-i18n="sort_most_upvoted">Most Upvoted</option>
                                </select>
                            </div>
                            <div class="mt-6">
                                <h5 class="font-semibold mb-2" data-i18n="categories_title">Categories</h5>
                                <div id="categories-filter" class="space-y-2">
                                    <!-- Category filters will be injected here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </aside>

                <!-- Product List -->
                <div id="product-list" class="w-full lg:w-3/4 space-y-4">
                    <!-- Product items will be injected here by JavaScript -->
                </div>
            </div>
        </section>

    </main>

    <script src="script.js"></script>
</body>
</html>