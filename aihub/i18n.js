let translations = {};

const translationsData = {
    en: {
        "nav_trending": "Trending",
        "nav_new": "New",
        "nav_submit": "Submit",
        "nav_login": "Login",
        "nav_submit_product": "Submit Product",
        "search_placeholder": "Search AI products...",
        "hero_pre_title": "The community-driven AI product platform",
        "hero_title_1": "Discover, launch and discuss the",
        "hero_title_2": "best",
        "hero_title_3": "AI products",
        "hero_subtitle": "Join the community of makers and users exploring the rapidly evolving AI ecosystem. Find tools that solve real problems.",
        "hero_search_placeholder": "Search AI tools...",
        "hero_search_button": "Submit",
        "popular_categories": "Popular categories:",
        "cat_writing": "Writing",
        "cat_image_generation": "Image Generation",
        "cat_productivity": "Productivity",
        "featured_collections": "Featured Collections",
        "view_all": "View all",
        "trending_products": "Trending Products",
        "filters_title": "Filters",
        "filters_clear": "Clear",
        "sort_by": "Sort by",
        "sort_popular_today": "Popular Today",
        "sort_newest": "Newest",
        "sort_most_upvoted": "Most Upvoted",
        "categories_title": "Categories",
        "cat_all": "All",
        "cat_development": "Development",
        "cat_video_creation": "Video Creation",
        "cat_design": "Design",
        "cat_education": "Education",
        "cat_finance": "Finance",
        "no_products_found": "No products found."
    },
    zh: {
        "nav_trending": "热门趋势",
        "nav_new": "最新发布",
        "nav_submit": "提交",
        "nav_login": "登录",
        "nav_submit_product": "提交产品",
        "search_placeholder": "搜索AI产品...",
        "hero_pre_title": "一个社区驱动的AI产品平台",
        "hero_title_1": "发现、发布和讨论",
        "hero_title_2": "最好的",
        "hero_title_3": "AI产品",
        "hero_subtitle": "加入创作者和用户社区，探索快速发展的AI生态系统。寻找能解决实际问题的工具。",
        "hero_search_placeholder": "搜索AI工具...",
        "hero_search_button": "提交",
        "popular_categories": "热门分类:",
        "cat_writing": "写作",
        "cat_image_generation": "图像生成",
        "cat_productivity": "生产力",
        "featured_collections": "精选合集",
        "view_all": "查看全部",
        "trending_products": "热门产品",
        "filters_title": "筛选",
        "filters_clear": "清除",
        "sort_by": "排序方式",
        "sort_popular_today": "今日热门",
        "sort_newest": "最新",
        "sort_most_upvoted": "最多点赞",
        "categories_title": "分类",
        "cat_all": "全部",
        "cat_development": "开发",
        "cat_video_creation": "视频创作",
        "cat_design": "设计",
        "cat_education": "教育",
        "cat_finance": "金融",
        "no_products_found": "未找到任何产品。"
    }
};

async function loadTranslations(lang) {
    translations = translationsData[lang] || translationsData['en'];
}
function translatePage() {
    document.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        const translation = translations[key];
        if (translation) {
            if ('placeholder' in element) {
                element.placeholder = translation;
            } else {
                element.textContent = translation;
            }
        }
    });
}

async function setLanguage(lang) {
    localStorage.setItem('language', lang);
    document.documentElement.lang = lang;
    await loadTranslations(lang);
    translatePage();
}

function getInitialLanguage() {
    return localStorage.getItem('language') || (navigator.language.startsWith('zh') ? 'zh' : 'en');
}

document.addEventListener('DOMContentLoaded', async () => {
    const initialLang = getInitialLanguage();
    await setLanguage(initialLang);
    // Any dynamic content rendering should be re-triggered after language change.
    // We might need to re-render products if their text is hardcoded in the render function.
});
