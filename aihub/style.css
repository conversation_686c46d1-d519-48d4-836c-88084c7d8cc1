@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

:root {
    --indigo-600: #4f46e5;
}

body {
    font-family: 'Inter', sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Ensure icons from Font Awesome are handled correctly */
.fa-sun, .fa-moon {
    transition: color 0.3s ease;
}

/* New Product Item Styling */
.product-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e5e7eb; /* gray-200 */
    border-radius: 0.5rem; /* rounded-lg */
    transition: background-color 0.2s ease, border-color 0.2s ease;
}

.product-item:hover {
    background-color: #f9fafb; /* gray-50 */
    border-color: #d1d5db; /* gray-300 */
}

.dark .product-item {
    border-color: #374151; /* gray-700 */
}

.dark .product-item:hover {
    background-color: #1f2937; /* gray-800 */
    border-color: #4b5563; /* gray-600 */
}

.product-item .upvote-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    border: 1px solid #e5e7eb; /* gray-200 */
    border-radius: 0.5rem;
    margin-right: 1rem;
    width: 60px;
    height: 60px;
    transition: border-color 0.2s ease, background-color 0.2s ease;
}

.dark .product-item .upvote-button {
    border-color: #374151; /* gray-700 */
}

.product-item .upvote-button.upvoted {
    border-color: var(--indigo-600);
    background-color: #e0e7ff; /* indigo-100 */
}

.dark .product-item .upvote-button.upvoted {
    background-color: rgba(79, 70, 229, 0.2);
}

/* Collection Card Styling */
.collection-card {
    position: relative;
    overflow: hidden;
    border-radius: 0.75rem; /* rounded-xl */
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    height: 280px;
}

.collection-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.collection-card:hover img {
    transform: scale(1.05);
}

.collection-card .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%);
}

.collection-card .card-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1.5rem;
    color: white;
}
