document.addEventListener('DOMContentLoaded', () => {
    let allProducts = [];
    let allCategories = [];

    const initialMockProducts = [
        {
            id: 1, name: 'DevGPT', tagline: 'Your AI pair programmer that writes and explains code', upvotes: 1284, comments: 97, category: 'Development', tags: ['Development', 'Coding', 'Productivity'], icon: 'https://picsum.photos/seed/1/80/80', date: '2024-07-14'
        },
        {
            id: 2, name: 'PixelMind', tagline: 'Generate stunning images from text prompts in seconds', upvotes: 1102, comments: 85, category: 'Image Generation', tags: ['Image Generation', 'Creative'], icon: 'https://picsum.photos/seed/2/80/80', date: '2024-09-22'
        },
        {
            id: 3, name: 'CopyWise', tagline: 'AI-powered copywriting for marketing and sales', upvotes: 950, comments: 72, category: 'Writing', tags: ['Writing', 'Marketing'], icon: 'https://picsum.photos/seed/3/80/80', date: '2024-09-21'
        },
        {
            id: 4, name: 'DataSphere', tagline: 'Analyze complex datasets with natural language', upvotes: 875, comments: 63, category: 'Productivity', tags: ['Productivity', 'Data'], icon: 'https://picsum.photos/seed/4/80/80', date: '2024-09-20'
        },
        {
            id: 5, name: 'AVIX', tagline: 'Create professional videos from simple text descriptions', upvotes: 812, comments: 55, category: 'Video Creation', tags: ['Video Creation', 'Editing'], icon: 'https://picsum.photos/seed/5/80/80', date: '2024-09-19'
        },
        {
            id: 6, name: 'Designify', tagline: 'Create stunning designs with AI.', upvotes: 210, comments: 30, category: 'Design', tags: ['Design', 'Art'], icon: 'https://picsum.photos/seed/6/80/80', date: '2024-09-18'
        },
        {
            id: 7, name: 'EduBot', tagline: 'Personalized learning paths for every student', upvotes: 650, comments: 41, category: 'Education', tags: ['Education', 'Learning'], icon: 'https://picsum.photos/seed/7/80/80', date: '2024-09-17'
        },
        {
            id: 8, name: 'FinanceFlow', tagline: 'Automate your financial planning.', upvotes: 132, comments: 25, category: 'Finance', tags: ['Finance', 'Productivity'], icon: 'https://picsum.photos/seed/8/80/80', date: '2024-09-16'
        }
    ];

    const mockCollections = [
        { title: 'Top AI Writing Tools', description: 'The best tools for content creation, copywriting, and editing', img: 'https://picsum.photos/seed/c1/400/280' },
        { title: 'Creative AI', description: 'Tools that enhance your creative work and design process', img: 'https://picsum.photos/seed/c2/400/280' },
        { title: 'AI for Developers', description: 'Enhance your coding workflow and productivity', img: 'https://picsum.photos/seed/c3/400/280' },
    ];

    const productList = document.getElementById('product-list');
    const collectionsGrid = document.getElementById('collections-grid');
    const categoriesFilter = document.getElementById('categories-filter');
    const themeToggle = document.getElementById('theme-toggle');
    const langSwitcher = document.getElementById('lang-switcher');

    function loadData() {
        const storedProducts = JSON.parse(localStorage.getItem('mockProducts')) || [];
        const productMap = new Map();
        initialMockProducts.forEach(p => productMap.set(p.id, p));
        storedProducts.forEach(p => productMap.set(p.id, p));
        allProducts = Array.from(productMap.values()).sort((a, b) => new Date(b.date) - new Date(a.date));
        const categories = new Set(allProducts.map(p => p.category));
        allCategories = ['All', ...categories];
    }

    function saveProducts() {
        localStorage.setItem('mockProducts', JSON.stringify(allProducts));
    }

    function renderCollections() {
        collectionsGrid.innerHTML = mockCollections.map(collection => `
            <div class="collection-card">
                <img src="${collection.img}" alt="${collection.title}">
                <div class="overlay"></div>
                <div class="card-content">
                    <h4 class="font-bold text-xl">${collection.title}</h4>
                    <p class="text-sm text-gray-200">${collection.description}</p>
                </div>
            </div>
        `).join('');
    }

    function renderCategories() {
        const categoryMap = {
            'All': 'cat_all',
            'Development': 'cat_development',
            'Writing': 'cat_writing',
            'Image Generation': 'cat_image_generation',
            'Video Creation': 'cat_video_creation',
            'Design': 'cat_design',
            'Education': 'cat_education',
            'Finance': 'cat_finance',
            'Productivity': 'cat_productivity'
        };
        categoriesFilter.innerHTML = allCategories.map((cat, index) => {
            const i18nKey = categoryMap[cat] || cat.toLowerCase();
            const translatedCat = (typeof translations !== 'undefined' && translations[i18nKey]) ? translations[i18nKey] : cat;
            return `
            <button data-category="${cat}" class="category-btn w-full text-left px-3 py-2 rounded-md text-sm font-medium flex justify-between items-center ${index === 0 ? 'bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300' : 'hover:bg-gray-100 dark:hover:bg-gray-800'}">
                    <span>${translatedCat}</span>
            </button>
            `;
        }).join('');
    }

    function renderProducts(products) {
        productList.innerHTML = products.length ? products.map(product => `
            <a href="product.html?id=${product.id}" class="product-item group">
                <img src="${product.icon}" alt="${product.name} icon" class="w-16 h-16 mr-4 rounded-lg">
                <div class="flex-grow">
                    <h4 class="font-semibold text-lg group-hover:text-indigo-600">${product.name}</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">${product.tagline}</p>
                    <div class="mt-2 flex items-center space-x-2">
                        ${product.tags.slice(0, 3).map(tag => `<span class="text-xs font-medium bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">${tag}</span>`).join('')}
                    </div>
                </div>
                <div class="flex items-center space-x-4 ml-4">
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-arrow-up"></i>
                        <span>${product.upvotes}</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-comment"></i>
                        <span>${product.comments}</span>
                    </div>
                    <div class="text-sm text-gray-500">
                        ${new Date(product.date).toLocaleDateString(document.documentElement.lang, { month: 'short', day: 'numeric' })}
                    </div>
                </div>
            </a>
        `).join('') : `<p class="text-center col-span-full py-8" data-i18n="no_products_found">${(typeof translations !== 'undefined' && translations.no_products_found) ? translations.no_products_found : 'No products found.'}</p>`;
    }

    function filterAndRender() {
        const selectedCategory = document.querySelector('.category-btn.bg-indigo-100')?.dataset.category || 'All';
        let filtered = allProducts;
        if (selectedCategory !== 'All') {
            filtered = allProducts.filter(p => p.category === selectedCategory);
        }
        renderProducts(filtered);
    }

    function rerenderAllComponents() {
        if (typeof translatePage === 'function') translatePage();
    renderCategories();
            filterAndRender();
        attachCategoryListeners();
    }

    themeToggle.addEventListener('click', () => {
        const isDark = document.documentElement.classList.toggle('dark');
        localStorage.setItem('darkMode', isDark);
});

    langSwitcher.addEventListener('click', async () => {
        const currentLang = localStorage.getItem('language') || 'en';
        const newLang = currentLang === 'en' ? 'zh' : 'en';
        if (typeof setLanguage === 'function') {
            await setLanguage(newLang);
            rerenderAllComponents();
        }
    });

    function attachCategoryListeners() {
        categoriesFilter.addEventListener('click', (e) => {
            const button = e.target.closest('.category-btn');
            if (button) {
                document.querySelectorAll('.category-btn').forEach(btn => {
                    btn.classList.remove('bg-indigo-100', 'dark:bg-indigo-900/50', 'text-indigo-700', 'dark:text-indigo-300');
                    btn.classList.add('hover:bg-gray-100', 'dark:hover:bg-gray-800');
                });
                button.classList.add('bg-indigo-100', 'dark:bg-indigo-900/50', 'text-indigo-700', 'dark:text-indigo-300');
                button.classList.remove('hover:bg-gray-100', 'dark:hover:bg-gray-800');
                filterAndRender();
            }
        });
    }

    if (localStorage.getItem('darkMode') === 'true') {
        document.documentElement.classList.add('dark');
    }

    setTimeout(() => {
        loadData();
        renderCollections();
        renderCategories();
        renderProducts(allProducts);
        attachCategoryListeners();
    }, 100);
});
