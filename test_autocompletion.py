from typing import List, Callable
import argparse

def insertion_sort(array: List[int]) -> List[int]:
    """插入排序"""
    arr = array.copy()
    for i in range(1, len(arr)):
        key = arr[i]
        j = i - 1
        while j >= 0 and key < arr[j]:
            arr[j + 1] = arr[j]
            j -= 1
        arr[j + 1] = key
    return arr

def quick_sort(arr: List[int]) -> List[int]:
    """快速排序"""
    if len(arr) <= 1:
        return arr.copy()
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quick_sort(left) + middle + quick_sort(right)

def merge_sort(arr: List[int]) -> List[int]:
    """归并排序"""
    if len(arr) <= 1:
        return arr.copy()
    mid = len(arr) // 2
    left_sorted = merge_sort(arr[:mid])
    right_sorted = merge_sort(arr[mid:])
    return merge(left_sorted, right_sorted)

def merge(left: List[int], right: List[int]) -> List[int]:
    """归并两个有序列表"""
    merged = []
    i = j = 0
    while i < len(left) and j < len(right):
        if left[i] <= right[j]:
            merged.append(left[i])
            i += 1
        else:
            merged.append(right[j])
            j += 1
    merged.extend(left[i:])
    merged.extend(right[j:])
    return merged

#生成一个冒泡排序算法
def bubble_sort(arr: List[int]) -> List[int]:
    """冒泡排序"""
    arr = arr.copy()
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

#生成一个选择排序算法
def selection_sort(arr: List[int]) -> List[int]:
    """选择排序"""
    arr = arr.copy()
    n = len(arr)
    for i in range(n):
        min_idx = i
        for j in range(i+1, n):
            if arr[j] < arr[min_idx]:
                min_idx = j
        arr[i], arr[min_idx] = arr[min_idx], arr[i]
    return arr

#生成一个堆排序算法
def heap_sort(arr: List[int]) -> List[int]:
    """堆排序"""
    arr = arr.copy()
    n = len(arr)
    for i in range(n//2-1, -1, -1):
        heapify(arr, n, i)
    for i in range(n-1, 0, -1):
        arr[i], arr[0] = arr[0], arr[i]
        heapify(arr, i, 0)
    return arr

def heapify(arr: List[int], n: int, i: int) -> None:
    """堆化"""
    largest = i
    left = 2 * i + 1
    right = 2 * i + 2
    if left < n and arr[left] > arr[largest]:
        largest = left
    if right < n and arr[right] > arr[largest]:
        largest = right
    if largest != i:
        arr[i], arr[largest] = arr[largest], arr[i]
        heapify(arr, n, largest)

#生成一个希尔排序算法
def shell_sort(arr: List[int]) -> List[int]:
    """希尔排序"""
    arr = arr.copy()
    n = len(arr)
    gap = n // 2
    while gap > 0:
        for i in range(gap, n):
            temp = arr[i]
            j = i
            while j >= gap and arr[j-gap] > temp:
                arr[j] = arr[j-gap]
                j -= gap
            arr[j] = temp
        gap //= 2
    return arr

    
def test_sort(arr: List[int]) -> None:
    """测试所有排序算法"""
    print(f"原始数组: {arr}")
    sorts = {
        '归并排序': merge_sort,
        '快速排序': quick_sort,
        '插入排序': insertion_sort,
        '冒泡排序': bubble_sort,
        '选择排序': selection_sort,
        '堆排序': heap_sort,
        '希尔排序': shell_sort,
    }
    sorted_arr = sorted(arr)
    for name, sort_func in sorts.items():
        # 创建数组副本进行排序，防止原地排序修改原始数组
        arr_copy = arr.copy()
        result = sort_func(arr_copy)
        # 如果排序函数是原地排序且返回 None，则 result 应取 arr_copy
        if result is None: # 虽然这里的函数都返回list，但保留这个检查更健壮
             result = arr_copy
        print(f"{name} 结果: {result}")
        assert result == sorted_arr, f"{name} 失败"
    print("所有排序算法均正确！")

def main():
    parser = argparse.ArgumentParser(description="测试排序算法")
    parser.add_argument('-a', '--array',
                        nargs='+',
                        type=int,
                        default=[3, 6, 8, 10, 1, 2, 1],
                        help="要排序的数组, 用空格分隔, 如: -a 3 5 1")
    args = parser.parse_args()
    test_sort(args.array)

if __name__ == '__main__':
    main()
