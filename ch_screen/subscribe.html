<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-white pb-20">
    <!-- 状态栏 -->
    <div class="h-14"></div>
    
    <!-- 顶部导航 -->
    <div class="px-6 py-4 flex justify-between items-center">
        <h1 class="text-2xl font-bold">我的订阅</h1>
        <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
            <i class="fas fa-search text-gray-600"></i>
        </button>
    </div>
    
    <!-- 分类标签 -->
    <div class="px-6 py-4">
        <div class="flex space-x-3 overflow-x-auto hide-scrollbar">
            <button class="px-4 py-2 bg-black text-white rounded-full text-sm whitespace-nowrap">
                全部
            </button>
            <button class="px-4 py-2 bg-gray-100 rounded-full text-sm whitespace-nowrap">
                已更新
            </button>
            <button class="px-4 py-2 bg-gray-100 rounded-full text-sm whitespace-nowrap">
                未听完
            </button>
            <button class="px-4 py-2 bg-gray-100 rounded-full text-sm whitespace-nowrap">
                已归档
            </button>
        </div>
    </div>
    
    <!-- 订阅列表 -->
    <div class="px-6">
        <div class="space-y-6">
            <!-- 订阅项 -->
            <div class="flex space-x-4">
                <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618" 
                     class="w-20 h-20 rounded-xl object-cover">
                <div class="flex-1">
                    <h3 class="font-medium mb-1">科技早知道</h3>
                    <p class="text-sm text-gray-500 mb-2">36.2万 订阅</p>
                    <div class="flex items-center">
                        <span class="text-xs text-gray-400 mr-2">更新至第285期</span>
                        <span class="px-2 py-1 bg-gray-100 rounded-full text-xs">2集未听</span>
                    </div>
                </div>
                <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <i class="fas fa-ellipsis-v text-gray-600"></i>
                </button>
            </div>

            <div class="flex space-x-4">
                <img src="https://images.unsplash.com/photo-1519074069444-1ba4fff66d16" 
                     class="w-20 h-20 rounded-xl object-cover">
                <div class="flex-1">
                    <h3 class="font-medium mb-1">声动早咖啡</h3>
                    <p class="text-sm text-gray-500 mb-2">28.4万 订阅</p>
                    <div class="flex items-center">
                        <span class="text-xs text-gray-400 mr-2">更新至第198期</span>
                        <span class="px-2 py-1 bg-gray-100 rounded-full text-xs">全部已听</span>
                    </div>
                </div>
                <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <i class="fas fa-ellipsis-v text-gray-600"></i>
                </button>
            </div>

            <div class="flex space-x-4">
                <img src="https://images.unsplash.com/photo-1557672172-298e090bd0f1" 
                     class="w-20 h-20 rounded-xl object-cover">
                <div class="flex-1">
                    <h3 class="font-medium mb-1">创业内幕</h3>
                    <p class="text-sm text-gray-500 mb-2">15.8万 订阅</p>
                    <div class="flex items-center">
                        <span class="text-xs text-gray-400 mr-2">更新至第156期</span>
                        <span class="px-2 py-1 bg-gray-100 rounded-full text-xs">5集未听</span>
                    </div>
                </div>
                <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <i class="fas fa-ellipsis-v text-gray-600"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-6 py-4">
        <div class="flex justify-between items-center">
            <button class="flex flex-col items-center">
                <i class="fas fa-home text-gray-400"></i>
                <span class="text-xs mt-1 text-gray-400">发现</span>
            </button>
            <button class="flex flex-col items-center">
                <i class="fas fa-compass text-black"></i>
                <span class="text-xs mt-1 text-black">订阅</span>
            </button>
            <button class="flex flex-col items-center">
                <i class="fas fa-user text-gray-400"></i>
                <span class="text-xs mt-1 text-gray-400">我的</span>
            </button>
        </div>
    </div>
</body>
</html> 