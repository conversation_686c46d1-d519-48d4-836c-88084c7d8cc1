<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播客App原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background-color: #f5f5f5;
        }
        .phone-frame {
            width: 393px;
            height: 852px;
            border-radius: 55px;
            overflow: hidden;
            position: relative;
            background: white;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            border: 14px solid #1a1a1a;
        }
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 20px;
            z-index: 1000;
        }
        iframe {
            border: none;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body class="min-h-screen p-8 flex flex-wrap gap-8 justify-center items-start">
    <!-- 首页 -->
    <div class="phone-frame">
        <div class="dynamic-island"></div>
        <iframe src="home.html"></iframe>
    </div>
    
    <!-- 播放页面 -->
    <div class="phone-frame">
        <div class="dynamic-island"></div>
        <iframe src="player.html"></iframe>
    </div>
    
    <!-- 个人中心 -->
    <div class="phone-frame">
        <div class="dynamic-island"></div>
        <iframe src="profile.html"></iframe>
    </div>

    <!-- 在 body 标签内添加新的 phone-frame -->
    <!-- 订阅页面 -->
    <div class="phone-frame">
        <div class="dynamic-island"></div>
        <iframe src="subscribe.html"></iframe>
    </div>
    
    <!-- 搜索页面 -->
    <div class="phone-frame">
        <div class="dynamic-island"></div>
        <iframe src="search.html"></iframe>
    </div>
</body>
</html>