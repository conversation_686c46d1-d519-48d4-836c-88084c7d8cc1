<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播放页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-white">
    <!-- 状态栏 -->
    <div class="h-14"></div>
    
    <!-- 顶部导航 -->
    <div class="px-6 py-4 flex justify-between items-center">
        <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
            <i class="fas fa-chevron-down text-gray-600"></i>
        </button>
        <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
            <i class="fas fa-ellipsis-h text-gray-600"></i>
        </button>
    </div>
    
    <!-- 封面区域 -->
    <div class="px-12 py-8">
        <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618" 
             class="w-full aspect-square rounded-3xl shadow-2xl">
    </div>
    
    <!-- 信息区域 -->
    <div class="px-6 py-4">
        <h1 class="text-2xl font-bold mb-2">ChatGPT与未来工作</h1>
        <p class="text-gray-500">科技早知道</p>
    </div>
    
    <!-- 进度条 -->
    <div class="px-6 py-4">
        <div class="h-1 bg-gray-200 rounded-full">
            <div class="h-1 bg-black rounded-full" style="width: 45%"></div>
        </div>
        <div class="flex justify-between mt-2">
            <span class="text-sm text-gray-500">21:30</span>
            <span class="text-sm text-gray-500">48:00</span>
        </div>
    </div>
    
    <!-- 控制按钮 -->
    <div class="px-6 py-8 flex justify-between items-center">
        <button class="w-12 h-12 flex items-center justify-center">
            <i class="fas fa-backward-step text-2xl"></i>
        </button>
        <button class="w-12 h-12 flex items-center justify-center">
            <i class="fas fa-backward text-2xl"></i>
        </button>
        <button class="w-20 h-20 bg-black rounded-full flex items-center justify-center">
            <i class="fas fa-pause text-2xl text-white"></i>
        </button>
        <button class="w-12 h-12 flex items-center justify-center">
            <i class="fas fa-forward text-2xl"></i>
        </button>
        <button class="w-12 h-12 flex items-center justify-center">
            <i class="fas fa-forward-step text-2xl"></i>
        </button>
    </div>
    
    <!-- 底部功能区 -->
    <div class="px-6 py-4 flex justify-between items-center">
        <button class="w-12 h-12 flex items-center justify-center">
            <i class="fas fa-heart text-gray-400 text-xl"></i>
        </button>
        <button class="w-12 h-12 flex items-center justify-center">
            <i class="fas fa-comment text-gray-400 text-xl"></i>
        </button>
        <button class="w-12 h-12 flex items-center justify-center">
            <i class="fas fa-share text-gray-400 text-xl"></i>
        </button>
        <button class="w-12 h-12 flex items-center justify-center">
            <i class="fas fa-list text-gray-400 text-xl"></i>
        </button>
    </div>
</body>
</html> 