<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-white pb-20">
    <!-- 状态栏 -->
    <div class="h-14"></div>
    
    <!-- 个人信息区 -->
    <div class="px-6 py-4">
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center">
                <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde" 
                     class="w-20 h-20 rounded-full object-cover border-2 border-gray-100">
                <div class="ml-4">
                    <h1 class="text-xl font-bold mb-1"><PERSON></h1>
                    <p class="text-gray-500">@davidchen</p>
                </div>
            </div>
            <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                <i class="fas fa-cog text-gray-600"></i>
            </button>
        </div>
        
        <!-- 数据统计 -->
        <div class="flex justify-between mb-8">
            <div class="text-center">
                <p class="text-2xl font-bold">245</p>
                <p class="text-gray-500 text-sm">收听时长</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold">32</p>
                <p class="text-gray-500 text-sm">订阅播客</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold">128</p>
                <p class="text-gray-500 text-sm">喜欢节目</p>
            </div>
        </div>
    </div>
    
    <!-- 功能区 -->
    <div class="px-6">
        <div class="grid grid-cols-4 gap-4 mb-8">
            <button class="flex flex-col items-center p-4 bg-gray-50 rounded-2xl">
                <i class="fas fa-history text-xl mb-2"></i>
                <span class="text-sm">收听历史</span>
            </button>
            <button class="flex flex-col items-center p-4 bg-gray-50 rounded-2xl">
                <i class="fas fa-heart text-xl mb-2"></i>
                <span class="text-sm">我的喜欢</span>
            </button>
            <button class="flex flex-col items-center p-4 bg-gray-50 rounded-2xl">
                <i class="fas fa-download text-xl mb-2"></i>
                <span class="text-sm">下载内容</span>
            </button>
            <button class="flex flex-col items-center p-4 bg-gray-50 rounded-2xl">
                <i class="fas fa-clock text-xl mb-2"></i>
                <span class="text-sm">稍后再听</span>
            </button>
        </div>
    </div>
    
    <!-- 最近收听 -->
    <div class="px-6">
        <h2 class="text-lg font-semibold mb-4">最近收听</h2>
        <div class="space-y-4">
            <div class="flex space-x-4">
                <img src="https://images.unsplash.com/photo-1508700115892-45ecd05ae2ad" 
                     class="w-16 h-16 rounded-xl object-cover">
                <div class="flex-1">
                    <h3 class="font-medium mb-1">ChatGPT与未来工作</h3>
                    <p class="text-sm text-gray-500">科技早知道</p>
                </div>
                <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <i class="fas fa-play text-sm"></i>
                </button>
            </div>
            <div class="flex space-x-4">
                <img src="https://images.unsplash.com/photo-1557672172-298e090bd0f1" 
                     class="w-16 h-16 rounded-xl object-cover">
                <div class="flex-1">
                    <h3 class="font-medium mb-1">创业故事分享</h3>
                    <p class="text-sm text-gray-500">声动早咖啡</p>
                </div>
                <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <i class="fas fa-play text-sm"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-6 py-4">
        <div class="flex justify-between items-center">
            <button class="flex flex-col items-center">
                <i class="fas fa-home text-gray-400"></i>
                <span class="text-xs mt-1 text-gray-400">发现</span>
            </button>
            <button class="flex flex-col items-center">
                <i class="fas fa-compass text-gray-400"></i>
                <span class="text-xs mt-1 text-gray-400">订阅</span>
            </button>
            <button class="flex flex-col items-center">
                <i class="fas fa-user text-black"></i>
                <span class="text-xs mt-1 text-black">我的</span>
            </button>
        </div>
    </div>
</body>
</html> 