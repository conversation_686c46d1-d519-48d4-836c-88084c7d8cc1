<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-white pb-20">
    <!-- 状态栏 -->
    <div class="h-14"></div>
    
    <!-- 顶部导航 -->
    <div class="px-6 py-4 flex justify-between items-center">
        <h1 class="text-2xl font-bold">发现</h1>
        <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
            <i class="fas fa-search text-gray-600"></i>
        </button>
    </div>
    
    <!-- 推荐分类 -->
    <div class="px-6 py-4">
        <div class="flex space-x-3 overflow-x-auto hide-scrollbar">
            <button class="px-4 py-2 bg-black text-white rounded-full text-sm whitespace-nowrap">
                为你推荐
            </button>
            <button class="px-4 py-2 bg-gray-100 rounded-full text-sm whitespace-nowrap">
                新品推荐
            </button>
            <button class="px-4 py-2 bg-gray-100 rounded-full text-sm whitespace-nowrap">
                科技
            </button>
            <button class="px-4 py-2 bg-gray-100 rounded-full text-sm whitespace-nowrap">
                商业
            </button>
            <button class="px-4 py-2 bg-gray-100 rounded-full text-sm whitespace-nowrap">
                文化
            </button>
        </div>
    </div>
    
    <!-- 推荐内容 -->
    <div class="px-6">
        <!-- 热门播客 -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4">热门播客</h2>
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-gray-50 rounded-2xl p-4">
                    <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618" 
                         class="w-full h-32 object-cover rounded-xl mb-3">
                    <h3 class="font-medium mb-1">科技早知道</h3>
                    <p class="text-sm text-gray-500">36.2万 收听</p>
                </div>
                <div class="bg-gray-50 rounded-2xl p-4">
                    <img src="https://images.unsplash.com/photo-1519074069444-1ba4fff66d16" 
                         class="w-full h-32 object-cover rounded-xl mb-3">
                    <h3 class="font-medium mb-1">声动早咖啡</h3>
                    <p class="text-sm text-gray-500">28.4万 收听</p>
                </div>
            </div>
        </div>
        
        <!-- 最近更新 -->
        <div>
            <h2 class="text-lg font-semibold mb-4">最近更新</h2>
            <div class="space-y-4">
                <div class="flex space-x-4">
                    <img src="https://images.unsplash.com/photo-1508700115892-45ecd05ae2ad" 
                         class="w-20 h-20 rounded-xl object-cover">
                    <div class="flex-1">
                        <h3 class="font-medium mb-1">ChatGPT与未来工作</h3>
                        <p class="text-sm text-gray-500 mb-2">科技早知道</p>
                        <p class="text-xs text-gray-400">48分钟 · 2.1万次收听</p>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <img src="https://images.unsplash.com/photo-1557672172-298e090bd0f1" 
                         class="w-20 h-20 rounded-xl object-cover">
                    <div class="flex-1">
                        <h3 class="font-medium mb-1">创业故事分享</h3>
                        <p class="text-sm text-gray-500 mb-2">声动早咖啡</p>
                        <p class="text-xs text-gray-400">32分钟 · 1.8万次收听</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-6 py-4">
        <div class="flex justify-between items-center">
            <button class="flex flex-col items-center">
                <i class="fas fa-home text-black"></i>
                <span class="text-xs mt-1 text-black">发现</span>
            </button>
            <button class="flex flex-col items-center">
                <i class="fas fa-compass text-gray-400"></i>
                <span class="text-xs mt-1 text-gray-400">订阅</span>
            </button>
            <button class="flex flex-col items-center">
                <i class="fas fa-user text-gray-400"></i>
                <span class="text-xs mt-1 text-gray-400">我的</span>
            </button>
        </div>
    </div>
</body>
</html> 