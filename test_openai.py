from openai import OpenAI

client = OpenAI(
  base_url="http://30.162.238.8:12700/v1",
  api_key="a22a37d7-0008-493e-8c11-a22a6ba04711",
)

completion = client.chat.completions.create(
  extra_headers={},
  extra_body={},
  model="google/gemini-2.5-pro-preview",
  messages=[
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "解释这张图片"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://weiban-image-1258344705.cos-internal.ap-guangzhou.tencentcos.cn/yuuhong/test/hanfu-style.png"
          }
        }
      ]
    }
  ]
)
print(completion.choices[0].message.content)